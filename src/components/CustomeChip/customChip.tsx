import { warning } from '@/lib/theme/colors'
import { Chip, styled } from '@mui/material'
import { ReactElement } from 'react'

interface IProps {
	label?: string
	color?: string
	background?: string
	setShowAcceptOrRejectDialog?: (value: boolean) => void
	icon?: ReactElement
	onClick?: () => void
	appliedClass: string
	isSmall?: boolean
	maxWidth?: string
}

export const CustomChip = ({
	label,
	color,
	background,
	icon,
	appliedClass,
	onClick,
	setShowAcceptOrRejectDialog,
	isSmall = false,
	maxWidth,
}: IProps) => {
	const useCustomStyles = !appliedClass
	return (
		<StyledChip
			onMouseEnter={
				setShowAcceptOrRejectDialog && (() => setShowAcceptOrRejectDialog(true))
			}
			onClick={onClick}
			className={appliedClass}
			label={label}
			icon={icon}
			sx={{
				color: useCustomStyles ? color || warning.darkest : undefined,
				backgroundColor: useCustomStyles
					? background || warning.lightest
					: undefined,
				textTransform: 'capitalize',
				...(isSmall && {
					height: 20,
					'& .MuiChip-label': {
						padding: '2px 10px', // adjust horizontal padding
						fontSize: 12, // smaller text
					},
				}),
				...(maxWidth && {
					maxWidth: maxWidth,
				}),
			}}
		/>
	)
}

const StyledChip = styled(Chip)(({ theme }) => ({
	...theme.typography.subtitle2,
	'&.transparent': {
		color: theme.palette.common.black,
		background: theme.palette.common.white,
		fontWeight: theme.typography.caption.fontWeight,
		border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
	},
	'&.approved': {
		color: theme.palette.success.main,
		background: theme.palette.success.light,
	},
	'&.pending': {
		color: theme.palette.warning.dark,
		background: theme.palette.warning.light,
	},
	'&.rejected': {
		color: theme.palette.error.dark,
		background: theme.palette.error.light,
	},
	'&.deleted': {
		color: theme.palette.error.dark,
		background: theme.palette.error.light,
	},
	'&.compensated': {
		color: theme.palette.text.secondary,
		background: '#FFE0BE',
	},
	'&.lightGrey': {
		color: theme.palette.text.secondary,
		background: theme.palette.custom.grey[100],
		fontWeight: theme.typography.caption.fontWeight,
	},
	'&.addAssignClass': {
		background: theme.palette.primary.main,
		color: theme.palette.common.white,
		fontWeight: theme.typography.subtitle1.fontWeight,
	},
}))
