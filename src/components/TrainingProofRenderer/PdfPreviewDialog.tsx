import { Cancel, Download, Fullscreen } from '@mui/icons-material'
import {
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	IconButton,
	Link,
} from '@mui/material'
import { useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import { toast } from 'react-toastify'

// pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
	'pdfjs-dist/build/pdf.worker.min.mjs',
	import.meta.url
).toString()

interface IProps {
	open: boolean
	close: () => void
	pdfUrl: string
	showDownloadButton?: boolean
}

export const PdfPreviewDialog = ({
	pdfUrl,
	close,
	open,
	showDownloadButton = true,
}: IProps) => {
	const [numPages, setNumPages] = useState<number>()

	const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
		setNumPages(numPages)
	}

	const handleOpenInNewTab = async () => {
		try {
			const res = await fetch(pdfUrl)
			if (!res.ok) {
				throw new Error(`HTTP error! status: ${res.status}`)
			}
			const blob = await res.blob()

			const file = new Blob([blob], { type: 'application/pdf' })
			const blobUrl = URL.createObjectURL(file)

			window.open(blobUrl, '_blank')
			setTimeout(() => URL.revokeObjectURL(blobUrl), 10000)
		} catch (err) {
			toast('Failed to open PDF in Full screen')
		}
	}

	return (
		<Dialog
			open={open}
			onClose={close}
			maxWidth='sm'
			fullWidth
			sx={{
				'& .MuiPaper-root': {
					p: 3,
					minWidth: 300,
					'& ::-webkit-scrollbar': {
						display: 'none',
					},
					'-ms-overflow-style': 'none' /* IE and Edge */,
					'scrollbar-width': 'none' /* Firefox */,
				},
				'.react-pdf__Page': {
					marginBottom: '10px',
				},
			}}>
			<IconButton
				sx={{ position: 'absolute', right: 40, top: 6, cursor: 'pointer' }}
				onClick={handleOpenInNewTab}>
				<Fullscreen />
			</IconButton>
			<IconButton
				sx={{ position: 'absolute', right: 5, top: 5, cursor: 'pointer' }}
				onClick={close}>
				<Cancel />
			</IconButton>
			<DialogContent sx={{ display: 'flex', justifyContent: 'center' }}>
				<Document file={pdfUrl} onLoadSuccess={onDocumentLoadSuccess}>
					{Array.from(Array(numPages).keys())?.map((x) => (
						<Page height={500} width={500} pageNumber={x + 1} key={x} />
					))}
				</Document>
			</DialogContent>
			{showDownloadButton ? (
				<DialogActions>
					<Button
						component={Link}
						href={pdfUrl}
						variant='contained'
						sx={{ textTransform: 'none' }}
						endIcon={<Download />}
						fullWidth>
						Download
					</Button>
				</DialogActions>
			) : null}
		</Dialog>
	)
}
