import { Close } from '@mui/icons-material'
import {
	I<PERSON><PERSON><PERSON><PERSON>,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import React, { FC, useEffect, useMemo, useState } from 'react'
import {
	AddAdmin,
	AddCsinkFarmerOperator,
	AddCsinkManager,
	AddManagerOrAdmin,
	AddOperator,
} from './subComponents'
import { useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import {
	AddOperatorTypeEnum,
	IEntitytabsCount,
	TModalTypeForUserManagement,
	User,
} from '@/interfaces'
import { PromoteCsinkNetworkOperator } from './subComponents/PromoteCsinkNetworkOperator'
import { AddComplianceManager } from './subComponents/AddComplianceManager'
import { AddCeresAuditor } from './subComponents/AddCeresAuditor'
import { theme } from '@/lib/theme/theme'

enum UserEnum {
	csink_manager = 'csink_manager',
	ba_admin = 'ba_admin',
	admin = 'admin',
	artisan_network_admin = 'artisan_network_admin',
	circonomy_employee = 'circonomy_employee',
	artisan_pro_Admin = 'artisan_pro_Admin',
	csink_network_admin = 'csink_network_admin',
	artisan_pro_operator = 'artisan_pro_operator',
	kiln_operator = 'kiln_operator',
	csink_operator_farmer = 'csink_operator_farmer',
	csink_network_operator = 'csink_network_operator',
	compliance_manager = 'compliance_manager',
	ceres_auditor = 'ceres_auditor',
	manager = 'manager',
	supervisor = 'supervisor',
}

const roleType: { [key: string]: string } = {
	[userRoles.CsinkManager]: UserEnum.csink_manager,
	[userRoles.BiomassAggregator]: UserEnum.ba_admin,
	[userRoles.Admin]: UserEnum.admin,
	[userRoles.CirconomyEmployee]: UserEnum.circonomy_employee,
	[userRoles.ArtisanPro]: UserEnum.artisan_pro_Admin,
	[userRoles.artisanProNetworkManager]: UserEnum.artisan_network_admin,
	[userRoles.cSinkNetwork]: UserEnum.csink_network_admin,
	[userRoles.artisanProOperator]: UserEnum.artisan_pro_operator,
	[userRoles.csink_operator_farmer]: UserEnum.csink_operator_farmer,
	[userRoles.csinkNetworkOperator]: UserEnum.csink_network_operator,
	[userRoles.compliance_manager]: UserEnum.compliance_manager,
	[userRoles.ceres_auditor]: UserEnum.ceres_auditor,
	[userRoles.Manager]: UserEnum.manager,
	[userRoles.Supervisor]: UserEnum.supervisor,
}

const RenderForm: FC<{
	type: UserEnum
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	user?: User
	modalType: TModalTypeForUserManagement
	addingTrainingImages?: boolean
}> = ({
	type,
	setIsActionInfoDrawer,
	user,
	modalType,
	addingTrainingImages = false,
}) => {
	const editMode = modalType === 'edit'
	switch (type) {
		case UserEnum.admin:
			return (
				<AddAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					editMode={editMode}
					userData={user}
				/>
			)
		case UserEnum.circonomy_employee:
			return (
				<AddAdmin
					isAdmin={false}
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					editMode={editMode}
					userData={user}
				/>
			)
		case UserEnum.csink_manager:
			return (
				<AddCsinkManager
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					editMode={editMode}
					userData={user}
				/>
			)
		case UserEnum.ba_admin:
			return (
				<AddManagerOrAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					entity='ba'
					editMode={editMode}
					user={user}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.artisan_network_admin:
			return (
				<AddManagerOrAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					entity='artisanProNetwork'
					editMode={editMode}
					user={user}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.artisan_pro_Admin:
			return (
				<AddManagerOrAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					entity='artisanPro'
					editMode={editMode}
					user={user}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.csink_network_admin:
			return (
				<AddManagerOrAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					entity='cSinkNetwork'
					editMode={editMode}
					user={user}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.manager:
			return (
				<AddManagerOrAdmin
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					entity='manager'
					editMode={editMode}
					user={user}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.artisan_pro_operator:
			return (
				<AddOperator
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					operatorFor={AddOperatorTypeEnum.artisanPro}
					user={user}
					modalType={modalType}
					addingTrainingImages={addingTrainingImages}
				/>
			)
		case UserEnum.csink_network_operator:
			if (modalType === 'promote')
				return (
					<PromoteCsinkNetworkOperator
						setIsActionInfoDrawer={setIsActionInfoDrawer}
						operatorFor={AddOperatorTypeEnum.csink}
						user={user}
						modalType={modalType}
					/>
				)
			return (
				<AddOperator
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					operatorFor={AddOperatorTypeEnum.csink}
					user={user}
					modalType={modalType}
				/>
			)
		case UserEnum.supervisor:
			return (
				<AddOperator
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					operatorFor={AddOperatorTypeEnum.supervisor}
					user={user}
					modalType={modalType}
				/>
			)
		case UserEnum.compliance_manager:
			return (
				<AddComplianceManager
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					user={user}
					editmode={editMode}
				/>
			)
		case UserEnum.ceres_auditor:
			return (
				<AddCeresAuditor
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					user={user}
					editMode={editMode}
				/>
			)
		// case UserEnum.kiln_operator:
		// 	return (
		// 		<AddOperator
		// 			setIsActionInfoDrawer={setIsActionInfoDrawer}
		// 			operatorFor='kiln'
		// 			user={user}
		// 			modalType={modalType}
		// 		/>
		// 	)
		case UserEnum.csink_operator_farmer:
			return (
				<AddCsinkFarmerOperator
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					user={user}
					editmode={editMode}
				/>
			)
		default:
			return null
	}
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	userData?: User
	modalType: TModalTypeForUserManagement
	entityTabsCount?: IEntitytabsCount
	addingTrainingImages?: boolean
}

export const AddUser: FC<TProps> = ({
	setIsActionInfoDrawer,
	userData,
	modalType,
	entityTabsCount,
	addingTrainingImages = false,
}) => {
	const { userDetails } = useAuthContext()
	const [selectedOption, setSelectedOption] = useState<UserEnum | string>('')

	const options = useMemo(
		() => [
			{
				label: 'Admin',
				value: UserEnum.admin,
				show: [userRoles.Admin]?.includes(
					userDetails?.accountType as userRoles
				),
			},
			{
				label: 'Circonomy Employee',
				value: UserEnum.circonomy_employee,
				show: [userRoles.Admin]?.includes(
					userDetails?.accountType as userRoles
				),
			},
			{
				label: 'Csink Manager Admin',
				value: UserEnum.csink_manager,
				show:
					[userRoles.Admin]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.csinkManagerCount ?? 0) > 0,
			},
			{
				label: 'BA Manager',
				value: UserEnum.ba_admin,
				show:
					[userRoles.Admin, userRoles.CsinkManager]?.includes(
						userDetails?.accountType as userRoles
					) && (entityTabsCount?.baCount ?? 0) > 0,
			},
			{
				label: 'Compliance Manager',
				value: UserEnum.compliance_manager,
				show: [userRoles.Admin]?.includes(
					userDetails?.accountType as userRoles
				),
			},
			{
				label: 'CERES Auditor',
				value: UserEnum.ceres_auditor,
				show: [userRoles.Admin]?.includes(
					userDetails?.accountType as userRoles
				),
			},
			{
				label: 'Artisan Pro Network Admin',
				value: UserEnum.artisan_network_admin,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.artisanProNetworkCount ?? 0) > 0,
			},
			{
				label: 'Artisan Pro Admin',
				value: UserEnum.artisan_pro_Admin,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.artisanProCount ?? 0) > 0,
			},
			{
				label: 'Csink Network Admin',
				value: UserEnum.csink_network_admin,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.csinkNetworkCount ?? 0) > 0,
			},
			{
				label: 'Artisan Pro Operator',
				value: UserEnum.artisan_pro_operator,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
						userRoles.ArtisanPro,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.artisanProCount ?? 0) > 0,
			},
			{
				label: 'Csink Network Farmer',
				value: UserEnum.csink_operator_farmer,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.cSinkNetwork,
						userRoles.BiomassAggregator,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.csinkNetworkCount ?? 0) > 0,
			},
			{
				label: 'Csink Network Operator',
				value: UserEnum.csink_network_operator,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.cSinkNetwork,
					]?.includes(userDetails?.accountType as userRoles) &&
					(entityTabsCount?.csinkNetworkCount ?? 0) > 0,
			},
		],
		[userDetails?.accountType, entityTabsCount]
	)

	useEffect(() => {
		if (!['edit', 'promote', 'demote'].includes(modalType)) return
		if (modalType === 'demote') {
			const roleTypeForDemote: { [key: string]: string } = {
				// managerChanges
				[userRoles.cSinkNetwork]: UserEnum.csink_network_operator,
				[userRoles.ArtisanPro]: UserEnum.artisan_pro_operator,
			}
			setSelectedOption(roleTypeForDemote[userData?.accountType as userRoles])
			return
		}
		setSelectedOption(roleType[userData?.accountType as userRoles])
	}, [modalType, userData?.accountType])

	const showTitle: { [key: string]: string } = useMemo(
		() => ({
			edit: 'Edit User',
			add: 'Add User',
			promote: 'Promote Operator',
			demote: 'Demote Admin',
		}),
		[]
	)

	const { showOptionField, errorMessage } = useMemo((): {
		showOptionField: boolean
		errorMessage: string
	} => {
		const accountType = userDetails?.accountType
		const isAddModal = ['add'].includes(modalType)

		if (!isAddModal) {
			return { showOptionField: false, errorMessage: '' }
		}

		const roleEntityMap: Partial<
			Record<
				userRoles,
				{ requiredEntities: (keyof IEntitytabsCount)[]; errorMessage: string }
			>
		> = {
			[userRoles.Admin]: {
				requiredEntities: [
					'artisanProCount',
					'artisanProNetworkCount',
					'baCount',
					'csinkNetworkCount',
					'csinkManagerCount',
				],
				errorMessage:
					'There is no Artisan pro or Csink Network created, please create one first.',
			},
			[userRoles.CsinkManager]: {
				requiredEntities: [
					'artisanProCount',
					'artisanProNetworkCount',
					'baCount',
					'csinkNetworkCount',
				],
				errorMessage:
					'There is no Artisan pro or Csink Network created, please create one first.',
			},
			[userRoles.BiomassAggregator]: {
				requiredEntities: [
					'artisanProCount',
					'artisanProNetworkCount',
					'csinkNetworkCount',
				],
				errorMessage:
					'There is no Artisan pro or Csink Network created, please create one first.',
			},
			[userRoles.artisanProNetworkManager]: {
				requiredEntities: ['artisanProCount'],
				errorMessage:
					'There is no Artisan pro created, please create one first.',
			},
			[userRoles.ArtisanPro]: {
				requiredEntities: ['artisanProCount'],
				errorMessage:
					'There is no Artisan pro created, please create one first.',
			},
			[userRoles.cSinkNetwork]: {
				requiredEntities: ['csinkNetworkCount'],
				errorMessage:
					'There is no Csink Network created, please create one first.',
			},
		}

		const roleConfig = roleEntityMap[accountType as userRoles]

		if (!roleConfig) {
			return { showOptionField: true, errorMessage: '' }
		}

		const { requiredEntities, errorMessage } = roleConfig
		const shouldHide = requiredEntities.every(
			(key) => (entityTabsCount?.[key] ?? 0) === 0
		)

		return {
			showOptionField: !shouldHide,
			errorMessage: shouldHide ? errorMessage : '',
		}
	}, [userDetails?.accountType, entityTabsCount, modalType])

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4' fontSize={theme.spacing(2)}>
						{showTitle[modalType]}
					</Typography>
					<IconButton onClick={() => setIsActionInfoDrawer(false)}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{showOptionField ? (
					<TextField
						value={selectedOption}
						onChange={(e) => {
							setSelectedOption(e.target.value)
						}}
						select
						label='Select User Type'>
						{options.map((option) =>
							option.show ? (
								<MenuItem key={option.value} value={option.value}>
									{option.label}
								</MenuItem>
							) : null
						)}
					</TextField>
				) : (
					errorMessage && (
						<Typography color='error' textAlign='center'>
							{errorMessage}
						</Typography>
					)
				)}
				{selectedOption !== '' ? (
					<RenderForm
						setIsActionInfoDrawer={setIsActionInfoDrawer}
						type={selectedOption as UserEnum}
						user={userData}
						modalType={modalType}
						addingTrainingImages={addingTrainingImages}
					/>
				) : null}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		textAlign: 'center',
		padding: theme.spacing(2, 4),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer': {
			gap: theme.spacing(2),
			button: {
				width: theme.spacing(30),
				height: theme.spacing(4.5),
				padding: theme.spacing(1, 2.5),
			},
		},
	},
}))
