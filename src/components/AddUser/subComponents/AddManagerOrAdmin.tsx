import { PhoneInputComponent } from '@/components/PhoneInput'
import { authAxios, useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import {
	Autocomplete,
	Button,
	FormControl,
	FormHelperText,
	MenuItem,
	Stack,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React, { FC, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { addBAManager, TAddBaManager } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-toastify'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import {
	IArtisanProNetworkDetails,
	ICsink,
	TArtisanProsResponse,
	User,
} from '@/interfaces'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { proxyImage } from '@/utils/helper'
import { AxiosError } from 'axios'
import { CustomTextField } from '@/utils/components'
import { useSearchParams } from 'react-router-dom'

const initialValues = {
	id: '',
	name: '',
	email: '',
	countryCode: '',
	number: '',
	trained: false,
	trainingImages: [],
	profileImage: null,
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	entity: 'ba' | 'cSinkNetwork' | 'artisanProNetwork' | 'artisanPro' | 'manager'
	editMode?: boolean
	user?: User
	addingTrainingImages: boolean
}

export const AddManagerOrAdmin: FC<TProps> = ({
	setIsActionInfoDrawer,
	entity,
	editMode = false,
	user,
	addingTrainingImages = false,
}) => {
	const theme = useTheme()
	const QueryClient = useQueryClient()
	const { userDetails } = useAuthContext()
	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || ''
	const {
		register,
		handleSubmit,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
	} = useForm<TAddBaManager>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddBaManager>(addBAManager),
	})

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)
			setValue('number', `${value}`)
			clearErrors('number')
		},
		[clearErrors, setValue]
	)

	//------------------ api section started --------------------------

	// ----------------- fetch apis started ------------------------------

	// all biomass aggregator
	const AllBaQuery = useQuery({
		queryKey: ['allBAForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				biomassAggregators: { id: string; name: string; shortName: string }[]
			}>(`/biomass-aggregator?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled:
			[userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			) &&
			entity === 'ba' &&
			!editMode,
	})

	// all artisan pro networks
	const fetchArtisanProNetworks = useQuery({
		queryKey: ['artisanProNetworkForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				count: number
				artisanProNetworks: IArtisanProNetworkDetails[]
			}>(`/new/artisanpro-networks?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.artisanProNetworks?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
			].includes(userDetails?.accountType as userRoles) &&
			entity === 'artisanProNetwork' &&
			!editMode,
	})

	// all c-sink networks
	const fetchCsinkNetworks = useQuery({
		queryKey: ['cSinkNetworkForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<ICsink>(
				`/new/csink-network?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.network?.map(
				(item: {
					id: string
					name: string
					shortName: string
				}): { label: string; value: string } => ({
					label: `${item.name} (${item.shortName})`,
					value: item.id,
				})
			) || [],
		enabled:
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.cSinkNetwork,
			].includes(userDetails?.accountType as userRoles) &&
			entity === 'cSinkNetwork' &&
			!editMode,
	})

	// all artisan pro
	const fetchArtisanPro = useQuery({
		queryKey: ['artisanProForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<TArtisanProsResponse>(
				`/new/artisan-pros?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.artisanPros?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.artisanProNetworkManager,
				userRoles.ArtisanPro,
			].includes(userDetails?.accountType as userRoles) &&
			entity === 'artisanPro' &&
			!editMode,
	})

	// ----------------- fetch apis ended ------------------------------

	// --------------- operational apis started --------------------------

	const editManagerMutation = useMutation({
		mutationKey: ['editManagerMutation'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, profileImage, ...rest } =
				values
			const payload = {
				...rest,
				countryCode: number ? countryCode : null,
				phoneNumber: number || null,
				trainingImageIds: trainingImages?.map((item) => item?.id),
				profileImageId: profileImage?.id ? profileImage?.id : null,
			}

			return await authAxios.put(`/user/${user?.id}`, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({
				queryKey: ['users'],
			})
			if (entityId)
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			else
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			setIsActionInfoDrawer(false)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})
	const addBaManagerMutation = useMutation({
		mutationKey: ['addBaManager'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, id, profileImage, ...rest } =
				values
			return await authAxios.post('/new/biomass-aggregator/assign-manger', {
				...rest,
				countryCode: number ? countryCode : null,
				number: number || null,
				trainingImages: trainingImages?.map((item) => item?.id),
				biomassAggregatorId: id,
				profileImageId: profileImage?.id ? profileImage?.id : null,
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const editBAManager = useMutation({
		mutationKey: ['editBaManager'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, profileImage, ...rest } =
				values
			return await authAxios.put(
				`/biomass-aggregator/${user?.biomassAggregatorId}/manager/${user?.id}`,
				{
					...rest,
					countryCode: number ? countryCode : null,
					number: number || null,
					trainingImages: trainingImages?.map((item) => item?.id),
					profileImageId: profileImage?.id ? profileImage?.id : null,
				}
			)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	// add/edit ba manager
	const handleAddOrEditBaManager = useCallback(
		async (values: TAddBaManager) =>
			editMode
				? editBAManager.mutate(values)
				: addBaManagerMutation.mutate(values),
		[addBaManagerMutation, editBAManager, editMode]
	)

	const addArtisanProNetworkAdminMutation = useMutation({
		mutationKey: ['addArtisanProNetworkAdmin'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, id, profileImage, ...rest } =
				values
			return await authAxios.post(`/artisan-pro-network/${id}/manager`, {
				...rest,
				countryCode: number ? countryCode : null,
				phoneNo: number || null,
				trainingImages: trainingImages?.map((item) => item?.id),
				profileImageId: profileImage?.id ? profileImage?.id : null,
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const editArtisanProNetworkAdminMutation = useMutation({
		mutationKey: ['editArtisanProNetworkAdmin'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, id, profileImage, ...rest } =
				values
			return await authAxios.put(
				`/artisan-pro-network/${id}/manager/${user?.id}`,
				{
					...rest,
					countryCode: number ? countryCode : null,
					number: number || null,
					trainingImages: trainingImages?.map((item) => item?.id),
					profileImageId: profileImage?.id ? profileImage?.id : null,
				}
			)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	// add/edit artisan pro network admin
	const handleAddOrEditArtisanProNetworkAdmin = useCallback(
		async (values: TAddBaManager) =>
			editMode
				? editArtisanProNetworkAdminMutation.mutate(values)
				: addArtisanProNetworkAdminMutation.mutate(values),
		[
			addArtisanProNetworkAdminMutation,
			editArtisanProNetworkAdminMutation,
			editMode,
		]
	)

	const addCsinkNetworkAdminMutation = useMutation({
		mutationKey: ['addCsinkNetworkAdmin'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, id, profileImage, ...rest } =
				values
			return await authAxios.post(`/cs-network-manager`, {
				...rest,
				countryCode: number ? countryCode : null,
				networkId: id,
				phoneNo: number || null,
				farmerId: null,
				trainingImages: trainingImages?.map((item) => item?.id),
				profileImageId: profileImage?.id ? profileImage?.id : null,
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	// add/edit c-sink network admin
	const handleAddOrEditCsinkNetworkAdmin = useCallback(
		async (values: TAddBaManager) =>
			editMode
				? editManagerMutation.mutate(values)
				: addCsinkNetworkAdminMutation.mutate(values),
		[addCsinkNetworkAdminMutation, editManagerMutation, editMode]
	)

	const addArtisanProAdminMutation = useMutation({
		mutationKey: ['addArtisanProAdmin'],
		mutationFn: async (values: TAddBaManager) => {
			const { trainingImages, number, countryCode, id, profileImage, ...rest } =
				values
			return await authAxios.post(`/new/artisan-pro/manager`, {
				...rest,
				countryCode: number ? countryCode : null,
				artisianProId: id,
				phoneNo: number || null,
				farmerId: null,
				trainingImages: trainingImages?.map((item) => item?.id),
				profileImageId: profileImage?.id ? profileImage?.id : null,
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	// add Artisan Pro admin
	const handleAddOrEditArtisanProAdmin = useCallback(
		async (values: TAddBaManager) =>
			editMode
				? editManagerMutation.mutate(values)
				: addArtisanProAdminMutation.mutate(values),
		[addArtisanProAdminMutation, editManagerMutation, editMode]
	)

	// --------------- operational apis ended --------------------------

	//--------------------- api section ended ------------------------

	const handleEditManager = useCallback(
		async (values: TAddBaManager) => {
			if (editMode) {
				editManagerMutation.mutate(values)
			}
		},
		[editManagerMutation, editMode]
	)
	const autoCompleteData = useMemo(
		() => ({
			ba: {
				options: AllBaQuery?.data || [],
				disabled: userDetails?.accountType === userRoles.BiomassAggregator,
				label: 'BA',
			},
			artisanProNetwork: {
				options: fetchArtisanProNetworks?.data || [],
				disabled:
					userDetails?.accountType === userRoles.artisanProNetworkManager,
				label: 'Artisan Pro Network',
			},
			cSinkNetwork: {
				options: fetchCsinkNetworks?.data || [],
				disabled: userDetails?.accountType === userRoles.cSinkNetwork,
				label: 'C-Sink Network',
			},
			manager: {
				options: fetchCsinkNetworks?.data || [],
				disabled: userDetails?.accountType === userRoles.Manager,
				label: 'Manager',
			},
			artisanPro: {
				options: fetchArtisanPro?.data || [],
				disabled: userDetails?.accountType === userRoles.ArtisanPro,
				label: 'Artisan Pro',
			},
		}),
		[
			AllBaQuery?.data,
			fetchArtisanPro?.data,
			fetchArtisanProNetworks?.data,
			fetchCsinkNetworks?.data,
			userDetails?.accountType,
		]
	)

	const formSubmitHandler = useCallback(
		(values: TAddBaManager) => {
			switch (entity) {
				case 'ba':
					handleAddOrEditBaManager(values)
					break
				case 'artisanProNetwork':
					handleAddOrEditArtisanProNetworkAdmin(values)
					break
				case 'artisanPro':
					handleAddOrEditArtisanProAdmin(values)
					break
				case 'cSinkNetwork':
					handleAddOrEditCsinkNetworkAdmin(values)
					break
				case 'manager':
					handleEditManager(values)
					break
				default:
					break
			}
		},
		[
			entity,
			handleAddOrEditBaManager,
			handleAddOrEditArtisanProNetworkAdmin,
			handleAddOrEditArtisanProAdmin,
			handleAddOrEditCsinkNetworkAdmin,
			handleEditManager,
		]
	)

	const isLoading = useMemo(
		() =>
			addBaManagerMutation.isPending ||
			addCsinkNetworkAdminMutation.isPending ||
			addArtisanProNetworkAdminMutation.isPending ||
			addArtisanProAdminMutation.isPending ||
			editBAManager.isPending ||
			editManagerMutation.isPending ||
			editArtisanProNetworkAdminMutation.isPending,
		[
			addArtisanProAdminMutation.isPending,
			addArtisanProNetworkAdminMutation.isPending,
			addBaManagerMutation.isPending,
			addCsinkNetworkAdminMutation.isPending,
			editManagerMutation.isPending,
			editArtisanProNetworkAdminMutation.isPending,
			editBAManager.isPending,
		]
	)

	useEffect(() => {
		if (!editMode) return
		setValue('name', user?.name || '')
		setValue('countryCode', user?.countryCode || '')
		setValue('email', user?.email || '')
		setValue('number', user?.number)
		setValue('profileImage', {
			id: user?.profileImageUrl?.id || '',
			url: user?.profileImageUrl?.id
				? proxyImage(user?.profileImageUrl?.path)
				: '',
		})
		setValue('trained', (user?.trainingImageUrls ?? [])?.length > 0 || false)
		setValue('trainingImages', user?.trainingImageUrls || [])
		let entityId = ''
		switch (entity) {
			case 'ba':
				entityId = user?.biomassAggregatorId || ''
				break
			case 'artisanProNetwork':
				entityId = user?.artisanProNetworkId || ''
				break
			case 'artisanPro':
				entityId = user?.artisanProId || ''
				break
			case 'cSinkNetwork':
				entityId = user?.csinkNetworkId || ''
				break
			case 'manager':
				entityId = user?.artisanProId ?? user?.csinkNetworkId ?? ''
				break
			default:
				entityId = ''
				break
		}
		setValue('id', entityId)
		if (addingTrainingImages) {
			setValue('trained', true)
		}
	}, [addingTrainingImages, editMode, entity, setValue, user])

	return (
		<Stack
			component='form'
			onSubmit={handleSubmit(formSubmitHandler)}
			mt={2}
			rowGap={2}>
			<Stack gap={2}>
				{autoCompleteData[entity].disabled || editMode ? null : (
					<Autocomplete
						disabled={autoCompleteData[entity].disabled}
						onChange={(_, newValue: any) => {
							setValue('id', newValue?.value)
							clearErrors('id')
						}}
						options={autoCompleteData[entity].options}
						renderInput={(params) => {
							return (
								<CustomTextField
									{...params}
									schema={addBAManager}
									label={autoCompleteData[entity].label}
									error={Boolean(errors?.id?.message)}
									helperText={errors?.id?.message}
									name='id'
								/>
							)
						}}
					/>
				)}
				<CustomProfileElement
					{...(editMode && { value: watch('profileImage') })}
					errorMessage={errors?.profileImage?.id?.message}
					setValue={(id, url) =>
						setValue('profileImage', {
							id,
							url,
						})
					}
					clearErrors={() => clearErrors('profileImage.id')}
				/>
				<CustomTextField
					schema={addBAManager}
					id='email'
					label='Email'
					{...register('email')}
					value={watch('email')}
					variant='outlined'
					fullWidth
					error={!!errors.email?.message}
					helperText={errors.email?.message}
				/>
				<CustomTextField
					schema={addBAManager}
					id='name'
					label='Name of Manager'
					variant='outlined'
					{...register('name')}
					value={watch('name')}
					fullWidth
					error={!!errors.name?.message}
					helperText={errors.name?.message}
				/>
				<FormControl fullWidth>
					<PhoneInputComponent
						value={watch('number') || ''}
						handleOnChange={handleOnChange}
						dialCode={watch('countryCode')}
						getSelectedCountryDialCode={(dialCode) =>
							setValue('countryCode', dialCode)
						}
					/>
				</FormControl>
				<FormControl fullWidth>
					<CustomTextField
						schema={addBAManager}
						select
						{...register('trained')}
						label='Trained'
						id='select-type'
						value={watch('trained')}
						onChange={(event) => {
							if (event.target.value !== 'true') {
								setValue('trainingImages', [])
							}
							setValue('trained', event.target.value === 'true')
							clearErrors('trained')
						}}
						error={!!errors.trained?.message}
						helperText={errors.trained?.message}>
						<MenuItem value='true'>Yes</MenuItem>
						<MenuItem value='false'>No</MenuItem>
					</CustomTextField>
				</FormControl>
				{watch('trained') === true && (
					<Stack>
						<Stack rowGap={2} width='100%'>
							<MultipleFileUploader
								{...(editMode && {
									data: (watch('trainingImages') ?? [])?.map((item) => ({
										...item,
										fileName: item?.path,
									})),
								})}
								heading='Upload or Drag the Training Document '
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('trainingImages', data)
									clearErrors('trainingImages')
								}}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.trainingImages)}>
							{errors?.trainingImages?.message}
						</FormHelperText>
					</Stack>
				)}
			</Stack>
			<Stack
				direction='row'
				justifyContent='space-between'
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>{' '}
				<LoadingButton
					loading={isLoading}
					disabled={isLoading}
					type='submit'
					variant='contained'>
					{editMode ? 'Save' : 'Add'}
				</LoadingButton>
			</Stack>
		</Stack>
	)
}
