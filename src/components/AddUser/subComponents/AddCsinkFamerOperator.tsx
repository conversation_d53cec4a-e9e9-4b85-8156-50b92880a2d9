import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { authAxios, useAuthContext } from '@/contexts'
import { ICsink, User } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { ICrop } from '@/types'
import { userRoles } from '@/utils/constant'
import { LoadingButton } from '@mui/lab'
import {
	Autocomplete,
	Button,
	FormHelperText,
	MenuItem,
	Stack,
	StackOwnProps,
	styled,
	Typography,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { toast } from 'react-toastify'
import {
	addCsinkFamerOperatorSchema,
	editCsinkFamerOperatorSchema,
} from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { CustomTextField } from '@/utils/components'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { InferType } from 'yup'
import { useSearchParams } from 'react-router-dom'

const genderlist = [
	{
		label: 'Male',
		value: 'male',
	},
	{
		label: 'Female',
		value: 'female',
	},
	{
		label: 'Other',
		value: 'other',
	},
]
const unitlist = [
	{
		label: 'Acre',
		value: 'acre',
	},
	{
		label: 'Hectare',
		value: 'hectare',
	},
]

const initialValues = (editmode: boolean, user?: User) => {
	const isEditing = editmode && user
	return {
		id: '',
		name: isEditing ? user?.name ?? '' : '',
		address: '',
		gender: '',
		cropId: '',
		landmark: '',
		fieldSizeUnit: '',
		trained: true,
		trainingImages: isEditing ? user?.trainingImageUrls ?? [] : [],
		...(isEditing && {
			email: user?.email ?? null,
			number: user?.number,
			countryCode: user?.countryCode,
		}),
	}
}

interface IProps {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	user?: User
	editmode?: boolean
}
export const AddCsinkFarmerOperator = ({
	setIsActionInfoDrawer,
	user,
	editmode = false,
}: IProps) => {
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()
	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || null

	const fetchCsinkNetworks = useQuery({
		queryKey: ['cSinkNetworkForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<ICsink>(
				`/new/csink-network?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.network?.map(
				(item: {
					id: string
					name: string
					shortName: string
				}): { label: string; value: string } => ({
					label: `${item.name} (${item.shortName})`,
					value: item.id,
				})
			),
	})
	const fetchCropList = useQuery({
		queryKey: ['fetchCropsList'],
		queryFn: async () => {
			const { data } = await authAxios.get<{ count: number; crops: ICrop[] }>(
				`/crops?limit=500`
			)
			return data
		},
		select: (data) =>
			data?.crops?.map((item) => ({
				label: item?.name,
				value: item?.id,
			})),
	})

	const schema = editmode
		? editCsinkFamerOperatorSchema
		: addCsinkFamerOperatorSchema
	type FormSchemaType = InferType<typeof schema>

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
		clearErrors,
		control,
	} = useForm<FormSchemaType>({
		defaultValues: initialValues(editmode, user),
		mode: 'all',
		resolver: yupResolver(schema),
	})

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)
			setValue('number', `${value}`)
			clearErrors('number')
		},
		[clearErrors, setValue]
	)
	const addFarmerMutation = useMutation({
		mutationKey: ['addFarmerOperator'],
		mutationFn: async ({
			id,
			landmark,
			fieldSize,
			fieldSizeUnit,
			farmLatitude,
			farmLongitude,
			email,
			number,
			countryCode,
			trainingImages,
			trained,
			...rest
		}: any) => {
			const trainingImageIds = trainingImages?.map(
				(item: { id: string }) => item.id
			)
			const payload = {
				...rest,
				email: email || null,
				farmDetails: {
					landmark,
					fieldSize,
					fieldSizeUnit,
					farmLatitude,
					farmLongitude,
				},
				trainingImageIds: trainingImageIds,
				isTrained: trained,
				...(number ? { number, countryCode } : {}),
			}
			const { status } = await authAxios.post(
				`/cs-network/${id}/farmers`,
				payload
			)
			return { status }
		},
		onSuccess: () => {
			toast('Farmer Operator added successfully')
			queryClient.invalidateQueries({
				queryKey: entityId
					? ['OrganizationSettingEntityUsers']
					: ['OrganizationSettingUsers'],
			})
			setIsActionInfoDrawer(false)
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const editFarmerMutation = useMutation({
		mutationKey: ['editFarmerOperator'],
		mutationFn: async ({
			id,
			email,
			trainingImages,
			number,
			countryCode,
			trained,
			...rest
		}: any) => {
			const trainingImageIds = trainingImages?.map(
				(item: { id: string }) => item.id
			)
			const payload = {
				...rest,
				email: email || null,
				trainingImageIds: trainingImageIds,
				isTrained: trained,
				...(number ? { number, countryCode } : {}),
			}
			const { status } = await authAxios.put(
				`/cs-network/${user?.csinkNetworkId}/operator-farmer/${user?.id}`,
				payload
			)
			return { status }
		},
		onSuccess: () => {
			toast('Farmer Operator edited successfully')
			queryClient.invalidateQueries({
				queryKey: entityId
					? ['OrganizationSettingEntityUsers']
					: ['OrganizationSettingUsers'],
			})
			setIsActionInfoDrawer(false)
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleFormSubmit = (formValues: FormSchemaType) => {
		if (editmode) {
			editFarmerMutation.mutate(formValues)
		} else {
			addFarmerMutation.mutate(formValues)
		}
	}

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('farmLatitude', Number(lat.toFixed(6)))
			setValue('farmLongitude', Number(lng.toFixed(6)))
			clearErrors(['farmLatitude', 'farmLongitude'])
		},
		[clearErrors, setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const autoCompleteData = useMemo(
		() => ({
			options: fetchCsinkNetworks?.data || [],
			disabled: userDetails?.accountType === userRoles.cSinkNetwork,
			label: 'C-Sink Network',
		}),
		[fetchCsinkNetworks?.data, userDetails?.accountType]
	)
	useEffect(() => {
		getCurrentLocation()
	}, [getCurrentLocation])

	useEffect(() => {
		if (autoCompleteData.options?.length === 1) {
			const onlyOneOption = autoCompleteData.options[0]
			setValue('id', onlyOneOption?.value)
			clearErrors('id')
		}
	}, [autoCompleteData.options, setValue, clearErrors])

	const watchTrainingImages =
		useWatch({ name: 'trainingImages', control })?.map((i) => ({
			...i,
			fileName: i?.path,
		})) ?? []
	return (
		<CustomStack component='form' onSubmit={handleSubmit(handleFormSubmit)}>
			<Stack gap={2}>
				{!editmode && (
					<>
						<Autocomplete
							value={
								autoCompleteData.options.find(
									(opt) => opt.value === watch('id')
								) || null
							}
							onChange={(_, newValue: any) => {
								setValue('id', newValue?.value)
								clearErrors('id')
							}}
							options={autoCompleteData.options}
							renderInput={(params) => (
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									{...params}
									name='id'
									label={autoCompleteData.label}
									error={Boolean(errors?.id?.message)}
									helperText={errors?.id?.message}
								/>
							)}
						/>
					</>
				)}
				<CustomTextField
					schema={addCsinkFamerOperatorSchema}
					id='name'
					label='Name'
					disabled={editmode}
					variant='outlined'
					placeholder='Enter Your name'
					fullWidth
					{...register('name')}
					error={!!errors.name?.message}
					helperText={errors.name?.message}
				/>
				<Stack>
					<PhoneInputComponent
						value={watch('number') || ''}
						dialCode={watch('countryCode')}
						handleOnChange={handleOnChange}
						isValid={!!errors.number?.message}
						getSelectedCountryDialCode={(dialCode) =>
							setValue('countryCode', dialCode)
						}
					/>
					<FormHelperText error={Boolean(errors?.number)} sx={{ ml: 2 }}>
						{errors?.number && (
							<Typography color='error' variant='caption'>
								{errors?.number?.message}
							</Typography>
						)}
					</FormHelperText>
				</Stack>
				<CustomTextField
					schema={addCsinkFamerOperatorSchema}
					id='email'
					label='Email'
					variant='outlined'
					placeholder='Enter Your email'
					fullWidth
					{...register('email')}
					error={!!errors.email?.message}
					helperText={errors.email?.message}
				/>
				<CustomTextField
					schema={addCsinkFamerOperatorSchema}
					select
					{...register('trained')}
					label='Trained'
					id='select-type'
					disabled
					value={watch('trained')}
					onChange={(event) => {
						setValue('trained', event.target.value === 'true')
						clearErrors('trained')
					}}
					error={!!errors.trained?.message}
					helperText={errors.trained?.message}>
					<MenuItem value='true'>Yes</MenuItem>
					<MenuItem value='false'>No</MenuItem>
				</CustomTextField>
				{watch('trained') === true && (
					<Stack>
						<Stack>
							<MultipleFileUploader
								data={
									watchTrainingImages?.map((i) => ({
										...i,
										fileName: i?.path,
									})) ?? []
								}
								heading='Upload or Drag the Training Images '
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('trainingImages', data)
									clearErrors('trainingImages')
								}}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.trainingImages)}>
							{errors.trainingImages && (
								<Typography color='error'>
									{errors?.trainingImages?.message}
								</Typography>
							)}
						</FormHelperText>
					</Stack>
				)}
				{!editmode && (
					<>
						<TwoColumnLayout
							spacing={2}
							left={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									id='address'
									label='Address'
									variant='outlined'
									placeholder='Enter Your address'
									fullWidth
									{...register('address')}
									error={!!errors.address?.message}
									helperText={errors.address?.message}
								/>
							}
							right={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									id='gender'
									label='Gender'
									variant='outlined'
									select
									placeholder='Enter Your gender'
									fullWidth
									{...register('gender')}
									error={!!errors.gender?.message}
									helperText={errors.gender?.message}>
									{genderlist?.map(({ label, value }, index) => (
										<MenuItem
											key={index}
											value={value}
											sx={{ textTransform: 'capitalize' }}>
											{label}
										</MenuItem>
									))}
								</CustomTextField>
							}
							gridBreakpoints={[6, 6]}
						/>
						<TwoColumnLayout
							spacing={2}
							left={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									id='fieldSize'
									label='Field Size'
									variant='outlined'
									placeholder='Enter Your fieldSize'
									fullWidth
									{...register('fieldSize')}
									error={!!errors.fieldSize?.message}
									helperText={errors.fieldSize?.message}
								/>
							}
							right={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									id='unit'
									label='Unit'
									variant='outlined'
									select
									placeholder='Enter Your unit'
									fullWidth
									{...register('fieldSizeUnit')}
									error={!!errors.fieldSizeUnit?.message}
									helperText={errors.fieldSizeUnit?.message}>
									{unitlist?.map(({ label, value }, index) => (
										<MenuItem
											key={index}
											value={value}
											sx={{ textTransform: 'capitalize' }}>
											{label}
										</MenuItem>
									))}
								</CustomTextField>
							}
							gridBreakpoints={[6, 6]}
						/>
						<CustomTextField
							schema={addCsinkFamerOperatorSchema}
							id='landmark'
							label='Landmark'
							variant='outlined'
							placeholder='Enter  landmark'
							fullWidth
							{...register('landmark')}
							error={!!errors.landmark?.message}
							helperText={errors.landmark?.message}
						/>

						<TwoColumnLayout
							spacing={2}
							left={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									watch={watch}
									id='farmLatitude'
									label='Farm Latitude'
									variant='outlined'
									placeholder='Enter Your Latitude'
									fullWidth
									{...register('farmLatitude')}
									error={!!errors.farmLatitude?.message}
									helperText={errors.farmLatitude?.message}
								/>
							}
							right={
								<CustomTextField
									schema={addCsinkFamerOperatorSchema}
									watch={watch}
									id='farmLongitude'
									label='Farm Longitude'
									variant='outlined'
									placeholder='Enter Your farmLongitude'
									fullWidth
									{...register('farmLongitude')}
									error={!!errors.farmLongitude?.message}
									helperText={errors.farmLongitude?.message}
								/>
							}
							gridBreakpoints={[6, 6]}
						/>
						<GoogleMapsWithNonDraggableMarker
							center={{
								lat: Number(watch('farmLatitude')),
								lng: Number(watch('farmLongitude')),
							}}
							setMapCenter={setMapCenter}
							mapContainerStyle={{
								textAlign: 'justify',
								width: '100%',
								height: 380,
								position: 'relative',
							}}
						/>

						<CustomTextField
							watch={watch}
							schema={addCsinkFamerOperatorSchema}
							id='cropId'
							label='Select Crop Name'
							variant='outlined'
							placeholder='Select Crop'
							fullWidth
							select
							SelectProps={{
								MenuProps: {
									PaperProps: {
										sx: {
											maxHeight: theme.spacing(20),
										},
									},
									anchorOrigin: {
										vertical: 'bottom',
										horizontal: 'center',
									},
								},
							}}
							{...register('cropId')}
							error={!!errors.cropId?.message}
							helperText={errors.cropId?.message}>
							{fetchCropList?.data?.map((item) => (
								<MenuItem key={item?.value} value={item?.value}>
									{item?.label}
								</MenuItem>
							))}
						</CustomTextField>
					</>
				)}
			</Stack>

			<Stack
				direction='row'
				justifyContent='space-between'
				mb={10}
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>
				<LoadingButton
					loading={addFarmerMutation.isPending}
					disabled={addFarmerMutation.isPending}
					type='submit'
					variant='contained'>
					Save
				</LoadingButton>
			</Stack>
		</CustomStack>
	)
}

interface CustomStackProps extends StackOwnProps {
	component?: React.ElementType
}
const CustomStack = styled(Stack)<CustomStackProps>(({ theme }) => ({
	marginTop: theme.spacing(2),
	rowGap: theme.spacing(9),
	'.show-subcolumns-btn': {
		fontSize: theme.typography.subtitle1.fontSize,
		fontWeight: theme.typography.subtitle1.fontWeight,
	},
}))
