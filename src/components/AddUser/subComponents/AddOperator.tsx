import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { authAxios, useAuthContext } from '@/contexts'
import {
	AddOperatorTypeEnum,
	ICsink,
	TArtisanProsResponse,
	TModalTypeForUserManagement,
	User,
} from '@/interfaces'
import { userRoles } from '@/utils/constant'
import {
	handleImageUpload,
	proxyImage,
	showAxiosErrorToast,
} from '@/utils/helper'
import { yupResolver } from '@hookform/resolvers/yup'
import { Add, CreateOutlined } from '@mui/icons-material'
import {
	Autocomplete,
	Avatar,
	Box,
	Button,
	CircularProgress,
	FormControl,
	FormHelperText,
	IconButton,
	InputLabel,
	MenuItem,
	OutlinedInput,
	Select,
	SelectChangeEvent,
	Stack,
	StackOwnProps,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React, {
	FC,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { addOperator, TAddOperator } from '../schema'
import { LoadingButton } from '@mui/lab'
import { Confirmation } from '@/components/Confirmation'
import { SelectArtisanPro } from '@/components/SelectArtisanPro'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { AssignKiln } from './AssignKiln'
import { CustomTextField } from '@/utils/components'
import { ILabelWithValue } from '@/types'
import { useSearchParams } from 'react-router-dom'
import { AxiosError } from 'axios'

const initialValues = {
	id: '',
	phoneNumber: '',
	countryCode: '',
	name: '',
	email: '',
	trained: false,
	aadhaarNumber: '',
	profileImage: {
		id: null,
		url: null,
	},
	aadhaarImage: '',
	kilnId: null,
	trainingImages: [],
	siteIds: [],
}

type TMutationPayload = {
	name: string
	phoneNo: string | null
	email: string | null
	countryCode: string | null | undefined
	profileImageId: string | null
	aadhaarNumber: string | null | undefined
	aadhaarImageID: string | null | undefined
	trainingImages: any[] | undefined
	id: string
	kilnId?: string
	siteId?: string
}
interface csinknetworkOperatorPayload extends TMutationPayload {
	siteIds?: string[]
	number?: string | null
}
type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	operatorFor: AddOperatorTypeEnum
	user?: User
	modalType: TModalTypeForUserManagement
	addingTrainingImages?: boolean
}

export const AddOperator: FC<TProps> = ({
	setIsActionInfoDrawer,
	operatorFor,
	user,
	modalType,
	addingTrainingImages = false,
}) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || ''
	const [profileLoading, setProfileLoading] = useState<boolean>(false)
	const [showModalForDemote, setShowModalForDemote] = useState<boolean>(false)
	const [showIdentificationProof, setShowIdentificationProof] =
		useState<boolean>(false)
	const [showAssignKilnField, setShowAssignKilnField] = useState<boolean>(false)
	const [urls, setUrls] = useState({
		profileImage: '',
		aadhaarImage: '',
	})
	const QueryClient = useQueryClient()
	const ref = useRef<HTMLLabelElement>(null)

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
		clearErrors,
		getValues,
	} = useForm<TAddOperator>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddOperator>(addOperator),
	})

	//----------------- api section started --------------

	//----------------- fetch api started --------------

	// artisan pro
	const fetchArtisanPro = useQuery({
		queryKey: ['artisanProForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<TArtisanProsResponse>(
				`/new/artisan-pros?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.artisanPros?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.artisanProNetworkManager,
				userRoles.ArtisanPro,
			].includes(userDetails?.accountType as userRoles) &&
			operatorFor === AddOperatorTypeEnum.artisanPro &&
			modalType === 'add',
	})

	// all c-sink networks
	const fetchCsinkNetworks = useQuery({
		queryKey: ['cSinkNetworkForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<ICsink>(
				`/new/csink-network?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.network?.map(
				(item: {
					id: string
					name: string
					shortName: string
				}): { label: string; value: string } => ({
					label: `${item.name} (${item.shortName})`,
					value: item.id,
				})
			),
		enabled:
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.cSinkNetwork,
			].includes(userDetails?.accountType as userRoles) &&
			operatorFor !== AddOperatorTypeEnum.artisanPro &&
			modalType === 'add',
	})

	//----------------- fetch api's ended ----------------

	// --------------- operational api's started ------

	const uploadPicture = useCallback(
		async (event: any, key: 'aadhaarImage' | 'profileImage') => {
			const file = event.target.files[0]
			if (key === 'profileImage') {
				setProfileLoading(true)
			}
			try {
				const data = await handleImageUpload(file)
				setValue(key, key === 'profileImage' ? data : data.id)
				setUrls((prev) => ({ ...prev, [key]: data.url }))
				if (key === 'profileImage') {
					clearErrors('profileImage')
				}
			} catch (err) {
				toast('Image upload failed')
			} finally {
				if (key === 'profileImage') {
					setProfileLoading(false)
				}
			}
		},
		[clearErrors, setValue]
	)

	const addKilnOperatorMutation = useMutation({
		mutationKey: ['addKilnOperator'],
		mutationFn: async ({ id, ...rest }: TMutationPayload) => {
			const { status } = await authAxios.post(
				`/cs-network/${id}/kiln-operator-only/create`,
				rest
			)
			return { status }
		},
		onSuccess: () => {
			toast('Operator added successfully')
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})
	const addCsinkNetworkOperatorMutation = useMutation({
		mutationKey: ['addCsinkNetworkOperator'],
		mutationFn: async ({
			id,
			trainingImages,
			...rest
		}: csinknetworkOperatorPayload) => {
			const { status } = await authAxios.post(`/cs-network/${id}/operator`, {
				...rest,
				trainingImageIds: trainingImages,
				trained: true,
			})
			return { status }
		},
		onSuccess: () => {
			toast('Operator added successfully')
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const editKilnOperatorMutation = useMutation({
		mutationKey: ['editKilnOperator'],
		mutationFn: async ({ id, ...rest }: TMutationPayload) => {
			const { data } = await authAxios.put(
				`/cs-network/${id}/updating-kiln-operator`,
				{ kilnOperatorId: user?.id, ...rest }
			)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const addArtisanProOperatorMutation = useMutation({
		mutationKey: ['addArtisanProOperator'],
		mutationFn: async ({ id, ...rest }: TMutationPayload) => {
			const { status } = await authAxios.post(
				`/artisian-pro/${id}/artisian-pro-operator/create`,
				rest
			)
			return { status }
		},
		onSuccess: () => {
			toast('Operator added successfully')
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const editSupervisorMutation = useMutation({
		mutationKey: ['editSupervisorMutation'],
		mutationFn: async (values: TMutationPayload) => {
			const {
				trainingImages,
				phoneNo,
				countryCode,
				profileImageId,
				// eslint-disable-next-line @typescript-eslint/no-unused-vars
				id,
				...rest
			} = values

			const payload = {
				...rest,
				countryCode: phoneNo ? countryCode : null,
				phoneNumber: phoneNo || null,
				trainingImageIds: trainingImages ?? [],
				profileImageId: profileImageId ?? null,
			}

			return await authAxios.put(`/user/${user?.id}`, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({
				queryKey: ['users'],
			})
			if (entityId)
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			else
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			setIsActionInfoDrawer(false)
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleCreateOrUpdateOperator = useCallback(
		async (values: TAddOperator) => {
			const formData = {
				name: values.name,
				phoneNo: values.phoneNumber || null,
				email: values.email || null,
				countryCode: values.phoneNumber ? values.countryCode : null,
				profileImageId: values?.profileImage?.id
					? values?.profileImage?.id
					: null,
				aadhaarNumber:
					values.aadhaarNumber === '' ? null : values.aadhaarNumber,
				aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
				trainingImages: values.trainingImages?.map(
					(trainingId) => trainingId?.id
				),
			}

			if (operatorFor === AddOperatorTypeEnum.kiln) {
				modalType === 'edit'
					? editKilnOperatorMutation.mutate({ id: values.id, ...formData })
					: addKilnOperatorMutation.mutate({
							id: values.id,
							kilnId: values?.kilnId ?? undefined,
							...formData,
					  })
			} else if (operatorFor === AddOperatorTypeEnum.csink) {
				if (user && modalType === 'edit') {
					editSupervisorMutation.mutate({
						id: values.id,
						...formData,
					})
				} else {
					addCsinkNetworkOperatorMutation.mutate({
						id: values.id,
						siteIds: values?.siteIds,
						number: values.phoneNumber,
						...formData,
					})
				}
			} else {
				modalType === 'edit'
					? editSupervisorMutation.mutate({
							id: values.id,
							...formData,
					  })
					: addArtisanProOperatorMutation.mutate({
							id: values.id,
							siteId: values?.kilnId ?? undefined,
							...formData,
					  })
			}
		},
		[
			addArtisanProOperatorMutation,
			addCsinkNetworkOperatorMutation,
			addKilnOperatorMutation,
			editSupervisorMutation,
			editKilnOperatorMutation,
			modalType,
			operatorFor,
			user,
		]
	)

	const promoteToArtisanProAdmin = useMutation({
		mutationKey: ['promoteToArtisanProAdmin'],
		mutationFn: async (values: TAddOperator) => {
			const payload = {
				name: values.name,
				phoneNo: values.phoneNumber || null,
				email: values.email || null,
				countryCode: values.phoneNumber ? values.countryCode : null,
				profileImageId: values?.profileImage?.id
					? values?.profileImage?.id
					: null,
				aadhaarNumber:
					values.aadhaarNumber === '' ? null : values.aadhaarNumber,
				aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
				trainingImages: values.trainingImages?.map(
					(trainingId) => trainingId?.id
				),
			}
			const { data } = await authAxios.post(
				`/artisan-pro-network/${user?.artisanProNetworkId}/artisian-pro/${user?.artisanProId}/operator/${user?.id}/promote`,
				payload
			)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const promoteToNetworkAdmin = useMutation({
		mutationKey: ['promoteToNetworkAdmin'],
		mutationFn: async (values: TAddOperator) => {
			const payload = {
				name: values.name,
				phoneNo: values.phoneNumber || null,
				email: values.email || null,
				countryCode: values.phoneNumber ? values.countryCode : null,
				profileImageId: values?.profileImage?.id
					? values?.profileImage?.id
					: null,
				aadhaarNumber:
					values.aadhaarNumber === '' ? null : values.aadhaarNumber,
				aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
				trainingImages: values.trainingImages?.map(
					(trainingId) => trainingId?.id
				),
				networkId: user?.csinkNetworkId,
				siteId: user?.id,
			}
			const { data } = await authAxios.post(`/cs-network-manager`, payload)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleSelectMultipleFarmers = (event: SelectChangeEvent<string[]>) => {
		const {
			target: { value },
		} = event

		const temp: ILabelWithValue[] = []
		;((value as string[]) ?? [])?.forEach((id: string) => {
			const obj = fetchFarmerListQuery?.data?.find((i) => i.value === id)
			if (obj) {
				temp.push(obj as ILabelWithValue)
			}
		})
		setValue(
			'siteIds',
			(temp ?? [])?.map((i) => i?.value)
		)
		clearErrors('siteIds')
	}

	const handlePromoteOperator = useCallback(
		(values: TAddOperator) =>
			user?.accountType === 'kiln_operator'
				? promoteToNetworkAdmin.mutate(values)
				: promoteToArtisanProAdmin.mutate(values),
		[promoteToArtisanProAdmin, promoteToNetworkAdmin, user?.accountType]
	)

	const demoteNetworkAdminToOperator = useMutation({
		mutationKey: ['demoteNetworkAdminToOperator'],
		mutationFn: async () => {
			const values = getValues()
			const payload = {
				profileImageId: values?.profileImage?.id
					? values?.profileImage?.id
					: null,
				aadhaarNumber:
					values.aadhaarNumber === '' ? null : values.aadhaarNumber,
				aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
				trainingImages: values.trainingImages?.map(
					(trainingId) => trainingId?.id
				),
				networkId: user?.csinkNetworkId,
			}
			const { data } = await authAxios.put(
				`/cs-network-manager/${user?.id}/demote-to-operator`,
				payload
			)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: (response) => {
			toast(response?.message)
			setShowModalForDemote(false)
			setIsActionInfoDrawer(false)
			QueryClient.refetchQueries({ queryKey: ['users'] })
		},
	})

	const handleDemoteNetworkToOperator = useCallback(() => {
		demoteNetworkAdminToOperator.mutate()
	}, [demoteNetworkAdminToOperator])

	// --------------- operational api's ended ------

	//----------------- api section ended --------------

	const autoCompleteData = useMemo(
		() => ({
			csinkOperator: {
				options: fetchCsinkNetworks?.data || [],
				disabled: userDetails?.accountType === userRoles.cSinkNetwork,
				label: 'C-Sink Network',
			},
			kiln: {
				options: fetchCsinkNetworks?.data || [],
				disabled: userDetails?.accountType === userRoles.cSinkNetwork,
				label: 'C-Sink Network',
			},
			artisanPro: {
				options: fetchArtisanPro?.data || [],
				disabled: userDetails?.accountType === userRoles.ArtisanPro,
				label: 'Artisan Pro',
			},
			supervisor: {
				options: [],
				disabled:
					userDetails?.accountType === userRoles.cSinkNetwork ||
					userDetails?.accountType === userRoles.ArtisanPro,
				label: 'Supervisor',
			},
		}),
		[fetchArtisanPro?.data, fetchCsinkNetworks?.data, userDetails?.accountType]
	)
	const fetchFarmerListQuery = useQuery({
		queryKey: ['fetchFarmerListQuery', watch('id')],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				farmers: {
					id: string
					name: string
					ShortName: string
					siteId: string
				}[]
			}>(`/cs-network/${watch('id')}/operator-farmer`)
			return data
		},
		select: (data) =>
			data?.farmers?.map((item) => ({
				label: item?.name,
				value: item?.siteId,
			})),
		enabled: operatorFor === AddOperatorTypeEnum.csink && !!watch('id'),
	})

	const handleFormSubmit = useCallback(
		(values: TAddOperator) => {
			switch (modalType) {
				case 'promote':
					handlePromoteOperator(values)
					break
				case 'demote':
					setShowModalForDemote(true)
					break
				default:
					handleCreateOrUpdateOperator(values)
					break
			}
		},
		[handleCreateOrUpdateOperator, handlePromoteOperator, modalType]
	)

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)
			setValue('phoneNumber', `${value}`)
			clearErrors('phoneNumber')
			clearErrors('email')
		},
		[clearErrors, setValue]
	)

	const isLoading = useMemo(
		() =>
			addArtisanProOperatorMutation.isPending ||
			addKilnOperatorMutation.isPending ||
			editKilnOperatorMutation.isPending ||
			editSupervisorMutation.isPending ||
			promoteToArtisanProAdmin.isPending ||
			promoteToNetworkAdmin.isPending,
		[
			addArtisanProOperatorMutation.isPending,
			addKilnOperatorMutation.isPending,
			editKilnOperatorMutation.isPending,
			editSupervisorMutation.isPending,
			promoteToArtisanProAdmin.isPending,
			promoteToNetworkAdmin.isPending,
		]
	)

	useEffect(() => {
		// setValue('trained', operatorFor !== AddOperatorTypeEnum.csink)
		setValue('trained', true)

		if (modalType === 'add') return
		setValue('name', user?.name || '')
		setValue('countryCode', user?.countryCode || '')
		setValue('email', user?.email || '')
		setValue('phoneNumber', user?.number)
		setValue('profileImage.id', user?.profileImageUrl?.id || '')
		setValue('profileImage.url', user?.profileImageUrl?.url || '')
		// setValue('trained', (user?.trainingImageUrls ?? [])?.length > 0 || false)
		setValue('trainingImages', user?.trainingImageUrls || [])
		setValue('aadhaarNumber', user?.aadhaarNumber || '')
		setValue('aadhaarImage', user?.aadhaarImageUrl?.id || '')
		setUrls({
			profileImage: proxyImage(user?.profileImageUrl?.path || ''),
			aadhaarImage: user?.aadhaarImageUrl?.path
				? proxyImage(user?.aadhaarImageUrl?.path || '')
				: '',
		})
		if (addingTrainingImages) {
			setValue('trained', true)
		}
		if (
			modalType === 'demote' &&
			!['network_admin', 'artisan_pro_admin'].includes(
				user?.accountType as userRoles
			)
		)
			return
		setValue(
			'id',
			operatorFor === AddOperatorTypeEnum.csink
				? user?.csinkNetworkId || ''
				: user?.artisanProId || ''
		)
	}, [addingTrainingImages, modalType, operatorFor, setValue, user])

	useEffect(() => {
		if (modalType === 'demote' && user?.accountType === userRoles.ArtisanPro)
			setValue('trained', true)
	}, [modalType, setValue, user?.accountType])

	const buttonText: { [key: string]: string } = {
		add: 'Add',
		edit: 'Save',
		promote: 'Promote',
		demote: 'Demote',
	}

	return (
		<>
			{showModalForDemote &&
			modalType === 'demote' &&
			user?.accountType === userRoles.cSinkNetwork ? (
				<Confirmation
					confirmationText={
						<Typography>
							Are you sure you want to demote this admin to operator ?
						</Typography>
					}
					open={showModalForDemote}
					handleClose={() => setShowModalForDemote(false)}
					handleNoClick={() => setShowModalForDemote(false)}
					handleYesClick={handleDemoteNetworkToOperator}
				/>
			) : null}
			{showModalForDemote &&
			modalType === 'demote' &&
			user?.accountType === userRoles.ArtisanPro ? (
				<SelectArtisanPro
					artisanPros={user?.artisanPros ?? []}
					open={showModalForDemote}
					onClose={() => setShowModalForDemote(false)}
					userDetails={getValues()}
					managerId={user?.id}
					artisanProNetworkId={user?.artisanProNetworkId}
					modalType='demoteArtisanProAdminToOperator'
					cb={() => {
						setShowModalForDemote(false)
						QueryClient.refetchQueries({ queryKey: ['users'] })
						setIsActionInfoDrawer(false)
					}}
				/>
			) : null}
			<CustomStack component='form' onSubmit={handleSubmit(handleFormSubmit)}>
				<Stack gap={2}>
					{modalType === 'add' ? (
						<Autocomplete
							onChange={(_, newValue: any) => {
								setValue('id', newValue?.value)
								clearErrors('id')
							}}
							options={autoCompleteData[operatorFor].options}
							renderInput={(params) => (
								<CustomTextField
									schema={addOperator}
									name='id'
									{...params}
									label={autoCompleteData[operatorFor].label}
									error={Boolean(errors?.id?.message)}
									helperText={errors?.id?.message}
								/>
							)}
						/>
					) : null}

					<Stack alignItems='center' gap={1}>
						{profileLoading ? (
							<Stack padding={2} borderRadius={1} bgcolor='custom.blue.A100'>
								<CircularProgress />
							</Stack>
						) : (
							<Stack
								ref={ref}
								htmlFor='profile-image-upload-input'
								component='label'
								position='relative'>
								<Avatar
									src={urls.profileImage || ''}
									alt='Profile error'
									sx={{
										width: 70,
										height: 70,
										border: errors?.profileImage
											? '1px solid red'
											: '1px solid black',
									}}
								/>

								<IconButton
									size='small'
									onClick={() => ref?.current?.click()}
									sx={{
										position: 'absolute',
										right: -15,
										bottom: -10,
										backgroundColor: 'grey.300',
										':hover': {
											backgroundColor: 'grey.300',
										},
									}}>
									<CreateOutlined fontSize='medium' color='inherit' />
								</IconButton>
								<input
									id='profile-image-upload-input'
									style={{
										width: '100%',
										height: '100%',
										cursor: 'pointer',
										position: 'absolute',
										opacity: 0,
									}}
									type='file'
									accept='image/*'
									onChange={(e) => uploadPicture(e, 'profileImage')}
								/>
							</Stack>
						)}

						<FormHelperText error={Boolean(errors?.profileImage)}>
							{errors?.profileImage && (
								<Typography color='error' variant='caption'>
									{errors?.profileImage?.message}
								</Typography>
							)}
						</FormHelperText>
					</Stack>
					<Stack>
						<PhoneInputComponent
							value={watch('phoneNumber') || ''}
							dialCode={watch('countryCode')}
							handleOnChange={handleOnChange}
							isValid={!!errors.phoneNumber?.message}
							getSelectedCountryDialCode={(dialCode) =>
								setValue('countryCode', dialCode)
							}
						/>
						<FormHelperText error={Boolean(errors?.phoneNumber)} sx={{ ml: 2 }}>
							{errors?.phoneNumber && (
								<Typography color='error' variant='caption'>
									{errors?.phoneNumber?.message}
								</Typography>
							)}
						</FormHelperText>
					</Stack>
					<CustomTextField
						schema={addOperator}
						id='email'
						label='Email'
						placeholder='Enter Your Email'
						variant='outlined'
						fullWidth
						{...register('email')}
						error={!!errors.email?.message}
						helperText={errors.email?.message}
					/>
					<CustomTextField
						schema={addOperator}
						id='name'
						label='Name'
						variant='outlined'
						placeholder='Enter Your name'
						fullWidth
						{...register('name')}
						error={!!errors.name?.message}
						helperText={errors.name?.message}
					/>
					{!showIdentificationProof && modalType !== 'edit' ? (
						<TwoColumnLayout
							gridBreakpoints={[0, 6]}
							left={
								<Button
									variant='text'
									className='show-subcolumns-btn'
									startIcon={<Add color='primary' />}
									onClick={() => setShowIdentificationProof(true)}>
									Add Identification Proof
								</Button>
							}
							right={<></>}
						/>
					) : null}
					{showIdentificationProof || modalType === 'edit' ? (
						<>
							<CustomTextField
								schema={addOperator}
								id='identification'
								label='Identification No.'
								placeholder='Enter Your Identification Number'
								variant='outlined'
								fullWidth
								{...register('aadhaarNumber')}
								error={!!errors.aadhaarNumber?.message}
								helperText={errors.aadhaarNumber?.message}
							/>
							<Stack>
								<Typography fontWeight='bold'> Upload ID Proof</Typography>
								<label htmlFor='inputAadhar'>
									<StyledBox>
										<CreateOutlined
											fontSize='medium'
											color='inherit'
											sx={{ position: 'absolute' }}
										/>
										{urls.aadhaarImage && (
											<Box
												component='img'
												src={urls.aadhaarImage || ''}
												height='90%'
												alt='Upload ID Proof'
											/>
										)}
										<input
											id='inputAadhar'
											type='file'
											accept='.jpg, .jpeg, .png, .heic, .webp'
											style={{ display: 'none' }}
											onChange={(e) => uploadPicture(e, 'aadhaarImage')}
										/>
									</StyledBox>
								</label>
								<FormHelperText error={Boolean(errors.aadhaarImage)}>
									{errors.aadhaarImage && (
										<Typography color='error'>
											{errors?.aadhaarImage?.message}
										</Typography>
									)}
								</FormHelperText>
							</Stack>
						</>
					) : null}

					{operatorFor === AddOperatorTypeEnum.csink && modalType !== 'edit' ? (
						<FormControl fullWidth>
							<InputLabel>Select Farmers</InputLabel>
							<Select
								id='siteId'
								placeholder='Select Farmers'
								value={watch('siteIds') ?? []}
								input={
									<OutlinedInput
										label='Select Farmers'
										sx={{
											borderRadius: '8px',
										}}
									/>
								}
								multiple
								MenuProps={{
									PaperProps: {
										sx: {
											maxHeight: theme.spacing(20),
										},
									},
									anchorOrigin: {
										vertical: 'bottom',
										horizontal: 'center',
									},
								}}
								onChange={handleSelectMultipleFarmers}
								error={!!errors.siteIds?.message}>
								{fetchFarmerListQuery?.data?.map((item) => (
									<MenuItem key={item?.value} value={item?.value}>
										{item?.label}
									</MenuItem>
								))}
							</Select>
						</FormControl>
					) : null}
					<FormControl fullWidth>
						<CustomTextField
							schema={addOperator}
							select
							{...register('trained')}
							label='Trained'
							id='select-type'
							value={watch('trained')}
							// disabled={operatorFor !== AddOperatorTypeEnum.csink}
							disabled
							onChange={(event) => {
								setValue('trained', event.target.value === 'true')
								clearErrors('trained')
							}}
							error={!!errors.trained?.message}
							helperText={errors.trained?.message}>
							<MenuItem value='true'>Yes</MenuItem>
							<MenuItem value='false'>No</MenuItem>
						</CustomTextField>
					</FormControl>
					{watch('trained') === true && (
						<Stack>
							<Stack>
								<MultipleFileUploader
									{...(modalType !== 'add' && {
										data: (watch('trainingImages') ?? [])?.map((i) => ({
											...i,
											fileName: i?.path,
										})),
									})}
									heading='Upload or Drag the Training Images '
									sx={{
										height: { xs: 100, md: 166 },
										width: '100%',
									}}
									imageHeight={100}
									setUploadData={(data) => {
										setValue('trainingImages', data)
										clearErrors('trainingImages')
									}}
								/>
							</Stack>
							<FormHelperText error={Boolean(errors.trainingImages)}>
								{errors.trainingImages && (
									<Typography color='error'>
										{errors?.trainingImages?.message}
									</Typography>
								)}
							</FormHelperText>
						</Stack>
					)}
					{!showAssignKilnField &&
					modalType !== 'edit' &&
					operatorFor !== AddOperatorTypeEnum.csink ? (
						<TwoColumnLayout
							gridBreakpoints={[0, 6]}
							left={
								modalType != 'promote' ? (
									<Button
										variant='text'
										className='show-subcolumns-btn'
										startIcon={<Add color='primary' />}
										onClick={() => setShowAssignKilnField(true)}>
										{operatorFor === AddOperatorTypeEnum.kiln
											? 'Assign Kiln'
											: 'Assign Site'}
									</Button>
								) : (
									<></>
								)
							}
							right={<></>}
						/>
					) : null}
					{showAssignKilnField ? (
						<AssignKiln
							operatorFor={operatorFor}
							networkId={watch('id')}
							setValue={(id: string) => setValue('kilnId', id)}
						/>
					) : null}
				</Stack>
				<Stack
					direction='row'
					justifyContent='space-between'
					mb={10}
					className='buttonContainer'>
					<Button
						onClick={() => setIsActionInfoDrawer(false)}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						type='submit'
						variant='contained'>
						{buttonText[modalType]}
					</LoadingButton>
				</Stack>
			</CustomStack>
		</>
	)
}

const StyledBox = styled(Box)(({ theme }) => ({
	position: 'relative',
	display: 'flex',
	justifyContent: 'center',
	alignItems: 'center',
	borderRadius: theme.spacing(1),
	height: theme.spacing(15.5),
	// '116px',
	border: '1px dashed grey',
	marginTop: '16px',
}))

interface CustomStackProps extends StackOwnProps {
	component?: React.ElementType
}
const CustomStack = styled(Stack)<CustomStackProps>(({ theme }) => ({
	marginTop: theme.spacing(2),
	rowGap: theme.spacing(2),
	'.show-subcolumns-btn': {
		fontSize: theme.typography.subtitle1.fontSize,
		fontWeight: theme.typography.subtitle1.fontWeight,
	},
}))
