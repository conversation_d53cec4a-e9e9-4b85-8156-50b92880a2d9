import { useState } from 'react'
import { IconButton, InputAdornment } from '@mui/material'
import { Close, Search } from '@mui/icons-material'
import { QueryInput } from '@/components'

interface ToggleSearchInputProps {
	queryKey: string
	placeholder?: string
	setPageOnSearch?: boolean
	className?: string
}

export const ToggleQueryInput = ({
	queryKey,
	placeholder = 'Search',
	setPageOnSearch = false,
	className,
}: ToggleSearchInputProps) => {
	const [showSearch, setShowSearch] = useState(false)

	return showSearch ? (
		<QueryInput
			autoFocus
			className={className}
			queryKey={queryKey}
			placeholder={placeholder}
			setPageOnSearch={setPageOnSearch}
			InputProps={{
				startAdornment: (
					<InputAdornment position='start'>
						<Search
							color='disabled'
							sx={{ fontSize: 18 }} // smaller icon
						/>
					</InputAdornment>
				),
				endAdornment: (
					<InputAdornment position='end'>
						<IconButton
							size='small'
							onClick={() => setShowSearch(false)}
							edge='end'>
							<Close color='disabled' sx={{ fontSize: 18 }} />
						</IconButton>
					</InputAdornment>
				),
			}}
		/>
	) : (
		<IconButton onClick={() => setShowSearch(true)} size='small'>
			<Search sx={{ fontSize: 18 }} color='disabled' />
		</IconButton>
	)
}
