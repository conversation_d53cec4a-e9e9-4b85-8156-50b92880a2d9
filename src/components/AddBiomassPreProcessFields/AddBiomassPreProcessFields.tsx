import { FormHelperText, MenuItem, Stack, Typography } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { dryingTypeEnum, shreddingTypeEnum } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import { CustomTextField } from '@/utils/components'
import { AnyObjectSchema } from 'yup'

interface IProps {
	editMode?: boolean
	schema?: AnyObjectSchema
}
export const AddBiomassPreProcessFields = ({ schema }: IProps) => {
	const {
		register,
		clearErrors,
		setValue,
		watch,
		formState: { errors },
	} = useFormContext()
	return (
		<Stack gap={theme.spacing(3)}>
			<CustomTextField
				select
				id='selectDryingType'
				schema={schema}
				value={watch('dryingType')}
				label='Select Drying Type'
				{...register('dryingType')}
				SelectProps={{
					MenuProps: {
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'center',
						},
					},
				}}>
				<MenuItem value={dryingTypeEnum.MachineDrying}>Machine Drying</MenuItem>
				<MenuItem value={dryingTypeEnum.SunDrying}>Sun Drying</MenuItem>
			</CustomTextField>

			<CustomTextField
				id='DryingType'
				multiline
				rows={4}
				label='Add Description'
				variant='outlined'
				schema={schema}
				autoComplete='off'
				fullWidth
				error={!!errors.dryingStrategy?.message}
				helperText={errors?.dryingStrategy?.message?.toString()}
				{...register('dryingStrategy')}
			/>
			<Stack rowGap={2} width='100%'>
				<Typography variant='subtitle1'>Upload Documents</Typography>
				<MultipleFileUploader
					sx={{
						height: { xs: 100, md: 150 },
						width: '100%',
					}}
					imageHeight={100}
					data={watch('dryingStrategyMedia')}
					training={false}
					heading='Add Documents'
					setUploadData={(data) => {
						setValue('dryingStrategyMedia', data)
						clearErrors('dryingStrategyMedia')
					}}
				/>
			</Stack>
			<FormHelperText error={Boolean(errors.dryingStrategyMedia)}>
				{errors?.dryingStrategyMedia?.message?.toString()}
			</FormHelperText>

			<CustomTextField
				select
				id='selectShreddingType'
				label='Select Shredding Type'
				value={watch('shreddingType')}
				schema={schema}
				{...register('shreddingType')}
				SelectProps={{
					MenuProps: {
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'center',
						},
					},
				}}>
				<MenuItem value={shreddingTypeEnum.Machine}>Machine</MenuItem>
				<MenuItem value={shreddingTypeEnum.Manual}>Manual</MenuItem>
			</CustomTextField>

			<CustomTextField
				id='ShreddingType'
				multiline
				rows={4}
				label='Add Description'
				variant='outlined'
				schema={schema}
				autoComplete='off'
				fullWidth
				error={!!errors.shreddingStrategy?.message}
				helperText={errors?.shreddingStrategy?.message?.toString()}
				{...register('shreddingStrategy')}
			/>
			<Stack rowGap={2} width='100%'>
				<Typography variant='subtitle1'>Upload Documents</Typography>
				<MultipleFileUploader
					sx={{
						height: { xs: 100, md: 150 },
						width: '100%',
					}}
					imageHeight={100}
					training={false}
					data={watch('shreddingStrategyMedia')}
					heading='Add Documents'
					setUploadData={(data) => {
						setValue('shreddingStrategyMedia', data)
						clearErrors('shreddingStrategyMedia')
					}}
				/>
			</Stack>
			{errors.shreddingStrategyMedia ? (
				<FormHelperText error={Boolean(errors.shreddingStrategyMedia)}>
					{errors?.shreddingStrategyMedia?.message?.toString()}
				</FormHelperText>
			) : null}
		</Stack>
	)
}
