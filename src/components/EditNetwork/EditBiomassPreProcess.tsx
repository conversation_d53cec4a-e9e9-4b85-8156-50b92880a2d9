import { FC, useCallback } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	<PERSON><PERSON>,
	Divider,
	IconButton,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import * as Yup from 'yup'
import { AddBiomassPreProcessFields } from '@/components/AddBiomassPreProcessFields'
import { AxiosError } from 'axios'
import { Close } from '@mui/icons-material'
import { IArtisanProDetails, INetwork } from '@/interfaces'

const schema = Yup.object({
	dryingType: Yup.string().nullable(),
	dryingStrategy: Yup.string().nullable(),
	dryingStrategyMedia: Yup.array()
		.of(
			Yup.object().shape({
				id: Yup.string(),
				url: Yup.string(),
			})
		)
		.nullable(),
	shreddingType: Yup.string().nullable(),
	shreddingStrategy: Yup.string().nullable(),
	shreddingStrategyMedia: Yup.array()
		.of(
			Yup.object().shape({
				id: Yup.string(),
				url: Yup.string(),
			})
		)
		.nullable(),
})

export type TEditBiomassPreProcess = Yup.InferType<typeof schema>

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	isCsink?: boolean
	artisanProDetails?: IArtisanProDetails
	csinkNetworkDetails?: INetwork
}

export const EditBiomassPreProcess: FC<TProps> = ({
	handleCloseDrawer,
	editMode,
	csinkNetworkDetails,
	isCsink = true,
	artisanProDetails,
}) => {
	const networkDetails = isCsink ? csinkNetworkDetails : artisanProDetails
	const initialValues = {
		dryingType: networkDetails?.biomassPreprocessingDetails?.dryingType || null,
		dryingStrategy:
			networkDetails?.biomassPreprocessingDetails?.dryingStrategy || null,
		dryingStrategyMedia:
			networkDetails?.biomassPreprocessingDetails?.dryingDocuments || [],
		shreddingType:
			networkDetails?.biomassPreprocessingDetails?.shreddingType || null,
		shreddingStrategy:
			networkDetails?.biomassPreprocessingDetails?.shreddingStrategy || null,
		shreddingStrategyMedia:
			networkDetails?.biomassPreprocessingDetails?.shreddingDocuments || [],
	}

	const theme = useTheme()
	const queryClient = useQueryClient()
	const form = useForm<TEditBiomassPreProcess>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TEditBiomassPreProcess>(schema),
	})

	const { handleSubmit } = form

	const editBiomassPreProcess = useMutation({
		mutationKey: ['editBiomassPreProcess'],
		mutationFn: async (formData: TEditBiomassPreProcess) => {
			const api = isCsink
				? `/cs-network/${csinkNetworkDetails?.id}/biomass-preprocessing`
				: `/artisan-pro-network/${artisanProDetails?.artisianProNetworkId}/artisian-pro/${artisanProDetails?.id}/biomass-preprocessing`
			const newPayload = {
				dryingStrategy: formData?.dryingStrategy,
				shreddingStrategy: formData?.shreddingStrategy,
				dryingType: formData?.dryingType,
				shreddingType: formData?.shreddingType,
				shreddingDocumentIds: formData?.shreddingStrategyMedia?.map(
					(item) => item.id
				),
				dryingDocumentIds: formData?.dryingStrategyMedia?.map(
					(item) => item?.id
				),
			}
			return await authAxios.put(api, newPayload)
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({
				queryKey: [isCsink ? 'cSinkNetworkDetails' : 'artisanProDetail'],
			})
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
			handleCloseDrawer()
		},
	})

	const handleEditBiomassPreProcess = useCallback(
		async (values: TEditBiomassPreProcess) => {
			editBiomassPreProcess.mutate(values)
		},
		[editBiomassPreProcess]
	)

	return (
		<Stack>
			<Stack padding={theme.spacing(2)}>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					justifyContent='space-between'>
					<Typography variant='h5'>Biomass Pre-Processing Strategy</Typography>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Divider />
			<FormProvider {...form}>
				<Stack gap={theme.spacing(3)} padding={theme.spacing(4)}>
					<AddBiomassPreProcessFields editMode={editMode} />
					<Stack direction='row' justifyContent='space-between' gap={2}>
						<Button
							onClick={handleCloseDrawer}
							sx={{ border: `1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>
						<LoadingButton
							loading={editBiomassPreProcess.isPending}
							disabled={editBiomassPreProcess.isPending}
							onClick={handleSubmit(handleEditBiomassPreProcess)}
							variant='contained'>
							Save
						</LoadingButton>
					</Stack>
				</Stack>
			</FormProvider>
		</Stack>
	)
}
