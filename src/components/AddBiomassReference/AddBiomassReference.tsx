import { Close } from '@mui/icons-material'
import { <PERSON>ton, IconButton, Stack, styled, Typography } from '@mui/material'
import { theme } from '@/lib/theme/theme'
import { useCallback, useEffect, useMemo } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { BiomassReference, EntityEnum } from '@/interfaces'
import { fieldSizeEnum, ICrop } from '@/types'
import { AddBiomassPreProcessFields } from './AddBiomassReferenceFields'
import { LoadingButton } from '@mui/lab'
import { toast } from 'react-toastify'
import { yupResolver } from '@hookform/resolvers/yup'
import { addBiomassReferenceSchema, TAddBiomassreference } from './schema'

interface IProps {
	onClose: () => void
	networkType: EntityEnum
	id: string
	editMode: boolean
	biomassRefValue?: BiomassReference
}

const handleRefetchQueries = (networkType: EntityEnum) => {
	switch (networkType) {
		case EntityEnum.cSinkManager:
			return 'allCsinkManager'
		case EntityEnum.cSinkNetwork:
			return 'allcSinkNetwork'
		case EntityEnum.aps:
			return 'allaps'
		case EntityEnum.ba:
			return 'allBA'
		default:
			return ''
	}
}

const getfetchBiomassTypeListApiRoute = (
	networkType: EntityEnum,
	id: string
) => {
	switch (networkType) {
		case EntityEnum.cSinkNetwork:
			return `/cs-network/${id}/biomass-reference/crops`
		case EntityEnum.aps:
			return `/artisian-pro/${id}/biomass-reference/crops`
		default:
			return `/crops?limit=5000`
	}
}

const initialValues = {
	biomass: [
		{
			id: Math.random().toString(36).substring(2, 9),
			biomassTypeId: '',
			fieldSize: 1,
			fieldSizeUnit: fieldSizeEnum.Hectare,
			biomassQuantity: null,
			documentIds: [],
		},
	],
}

export const AddBiomassReference = ({
	onClose,
	networkType,
	id,
	editMode,
	biomassRefValue,
}: IProps) => {
	const queryClient = useQueryClient()
	const fieldIndex = 0 //As for now only we can add/edit one at a time
	const getApiRoutes = useCallback(() => {
		switch (networkType) {
			case EntityEnum.cSinkManager:
				return 'csink-manager'
			case EntityEnum.aps:
				return 'artisian-pro'
			case EntityEnum.ba:
				return 'biomass-aggregator'
			case EntityEnum.cSinkNetwork:
				return 'cs-network'
		}
	}, [networkType])

	const fetchBiomassTypeList = useQuery({
		queryKey: ['fetchBiomassTypeList'],
		queryFn: async () => {
			const { data } = await authAxios.get<
				{ count: number; crops: ICrop[] } | { cropName?: string; id?: string }[]
			>(getfetchBiomassTypeListApiRoute(networkType, id))
			return data
		},

		select: (data) => {
			if (Array.isArray(data)) {
				return data?.map((item) => ({
					label: item?.cropName ?? '',
					value: item?.id ?? '',
				}))
			} else {
				return data?.crops?.map((item) => ({
					label: item?.name ?? '',
					value: item?.id ?? '',
				}))
			}
		},
	})

	const form = useForm<TAddBiomassreference>({
		defaultValues: initialValues,
		resolver: yupResolver(addBiomassReferenceSchema),
	})
	const { handleSubmit, setValue } = form

	useEffect(() => {
		if (!editMode || !biomassRefValue) return
		setValue(
			`biomass.${fieldIndex}.biomassTypeId`,
			biomassRefValue?.biomassTypeId
		)
		setValue(
			`biomass.${fieldIndex}.biomassQuantity`,
			biomassRefValue?.biomassQuantity
		)
		setValue(`biomass.${fieldIndex}.fieldSize`, biomassRefValue?.fieldSize)
		setValue(`biomass.${fieldIndex}.documentIds`, biomassRefValue?.documents)
	}, [biomassRefValue, editMode, setValue])

	const addBiomassReferencesMutation = useMutation({
		mutationKey: ['AddBiomassReferencesMutation', networkType],
		mutationFn: async (values: TAddBiomassreference) => {
			const apiRoute = `/${getApiRoutes()}/${id}/biomass-reference`
			const { data } = await authAxios.post(apiRoute, values)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient?.invalidateQueries({
				queryKey: [handleRefetchQueries(networkType)],
			})
			queryClient?.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})
	const handleSave = useCallback(
		(values: TAddBiomassreference) => {
			addBiomassReferencesMutation.mutate(values)
		},
		[addBiomassReferencesMutation]
	)

	const crops = useMemo(() => {
		const baseList = fetchBiomassTypeList?.data ?? []

		if (editMode && biomassRefValue) {
			return [
				...baseList,
				{
					label: biomassRefValue?.biomassName,
					value: biomassRefValue?.biomassTypeId ?? '',
				},
			]
		}

		return baseList
	}, [fetchBiomassTypeList?.data, editMode, biomassRefValue])

	return (
		<StyledStack>
			<Stack className='header'>
				<Typography variant='body2' fontSize={theme.spacing(2)}>
					Biomass Reference
				</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>
			<FormProvider {...form}>
				<Stack className='container'>
					<Stack gap={theme.spacing(2)}>
						<AddBiomassPreProcessFields fieldIndex={fieldIndex} crops={crops} />
					</Stack>
				</Stack>
				<Stack className='buttonContainer'>
					<LoadingButton
						loading={addBiomassReferencesMutation?.isPending}
						disabled={addBiomassReferencesMutation?.isPending}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						Save
					</LoadingButton>
					<Button
						onClick={onClose}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
				</Stack>
			</FormProvider>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		gap: theme.spacing(3),
		padding: theme.spacing(0, 2),
		flex: 1,
		'.reference-container': {
			flexDirection: 'row',
			justifyContent: 'space-between',
			width: '100%',
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
			padding: theme.spacing(2),
			'.label': {
				fontWeight: 500,
			},
		},
		'.refernceFields-container': {
			gap: theme.spacing(4),
			padding: theme.spacing(3, 0),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
	},
	'.buttonContainer': {
		gap: theme.spacing(2),
		flexDirection: 'row',
		padding: theme.spacing(2),
		button: {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			marginBottom: theme.spacing(5),
		},
	},
}))
