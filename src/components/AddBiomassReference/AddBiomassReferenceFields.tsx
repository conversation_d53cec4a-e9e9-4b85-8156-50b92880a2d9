import { useFormContext } from 'react-hook-form'
import { TwoColumnLayout } from '../TwoColumnLayout'
import {
	FormControl,
	FormHelperText,
	InputAdornment,
	MenuItem,
	Stack,
	TextField,
	Typography,
} from '@mui/material'
import { CustomTextField } from '@/utils/components'
import { theme } from '@/lib/theme/theme'
import { TAddBiomassreference } from './schema'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { fieldSizeEnum } from '@/types'

const unitlist = [
	{
		label: 'Hectare',
		value: fieldSizeEnum.Hectare,
	},
	{
		label: 'Acre',
		value: fieldSizeEnum.Acre,
	},
]
interface IProps {
	fieldIndex: number
	crops: {
		label: string
		value: string
	}[]
}
export const AddBiomassPreProcessFields = ({ fieldIndex, crops }: IProps) => {
	const {
		register,
		watch,
		clearErrors,
		setValue,
		formState: { errors },
	} = useFormContext<TAddBiomassreference>()

	return (
		<>
			<Stack className='refernceFields-container'>
				<TwoColumnLayout
					gridBreakpoints={[6, 6]}
					spacing={2}
					left={
						<FormControl fullWidth>
							<TextField
								select
								id='biomassTypeId'
								value={watch(`biomass.${fieldIndex}.biomassTypeId`)}
								label='Select Biomass Type'
								placeholder='Biomass Type'
								{...register(`biomass.${fieldIndex}.biomassTypeId`)}
								SelectProps={{
									MenuProps: {
										PaperProps: {
											sx: {
												maxHeight: theme.spacing(40),
											},
										},
										anchorOrigin: {
											vertical: 'bottom',
											horizontal: 'center',
										},
									},
								}}
								error={!!errors?.biomass?.[fieldIndex]?.biomassTypeId}
								helperText={
									errors?.biomass?.[fieldIndex]?.biomassTypeId?.message
								}>
								{crops?.map((item, idx) => (
									<MenuItem key={idx + item?.value} value={item?.value}>
										{item?.label}
									</MenuItem>
								))}
							</TextField>
						</FormControl>
					}
					right={
						<CustomTextField
							type='number'
							InputProps={{
								endAdornment: (
									<InputAdornment position='start'>kg</InputAdornment>
								),
							}}
							hideNumberArrows={true}
							onKeyDown={(e) => {
								if (e.key === '.') {
									e.preventDefault()
								}
							}}
							onPaste={(e) => {
								const pasted = e.clipboardData.getData('text')
								if (pasted.includes('.')) {
									e.preventDefault()
								}
							}}
							id='biomassQuantity'
							value={watch(`biomass.${fieldIndex}.biomassQuantity`)}
							label='Biomass Quantity'
							placeholder='Enter Biomass Quantity'
							error={!!errors?.biomass?.[fieldIndex]?.biomassQuantity}
							helperText={
								errors?.biomass?.[fieldIndex]?.biomassQuantity?.message
							}
							{...register(`biomass.${fieldIndex}.biomassQuantity`)}
						/>
					}
				/>
				<TwoColumnLayout
					gridBreakpoints={[6, 6]}
					spacing={2}
					right={
						<FormControl fullWidth>
							<TextField
								select
								id='fieldSizeUnit'
								value={watch(`biomass.${fieldIndex}.fieldSizeUnit`)}
								label='Select Field Unit'
								placeholder='Field Unit'
								{...register(`biomass.${fieldIndex}.fieldSizeUnit`)}
								SelectProps={{
									MenuProps: {
										anchorOrigin: {
											vertical: 'bottom',
											horizontal: 'center',
										},
									},
								}}>
								{unitlist?.map((item, idx) => (
									<MenuItem key={idx + item?.value} value={item?.value}>
										{item?.label}
									</MenuItem>
								))}
							</TextField>
						</FormControl>
					}
					left={
						<FormControl fullWidth>
							<CustomTextField
								hideNumberArrows
								type='number'
								id='fieldSize'
								value={watch(`biomass.${fieldIndex}.fieldSize`)}
								label='Field Size'
								placeholder='Enter Field Size'
								error={!!errors?.biomass?.[fieldIndex]?.fieldSize}
								helperText={errors?.biomass?.[fieldIndex]?.fieldSize?.message}
								{...register(`biomass.${fieldIndex}.fieldSize`)}
							/>
						</FormControl>
					}
				/>
			</Stack>
			<Stack rowGap={2} width='100%'>
				<Typography variant='subtitle1'>Upload Document</Typography>
				<MultipleFileUploader
					sx={{
						height: { xs: 100, md: 150 },
						width: '100%',
					}}
					imageHeight={100}
					{...register(`biomass.${fieldIndex}.documentIds`)}
					data={watch(`biomass.${fieldIndex}.documentIds`)}
					training={false}
					heading='Add Document'
					setUploadData={(data) => {
						setValue(`biomass.${fieldIndex}.documentIds`, data)
						clearErrors(`biomass.${fieldIndex}.documentIds`)
					}}
				/>
				{errors?.biomass?.[fieldIndex]?.documentIds?.message && (
					<FormHelperText error={true}>
						{errors?.biomass?.[fieldIndex]?.documentIds?.message}
					</FormHelperText>
				)}
			</Stack>
		</>
	)
}
