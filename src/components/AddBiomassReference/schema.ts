import * as yup from 'yup'
export const addBiomassReferenceSchema = yup.object({
	biomass: yup.array().of(
		yup.object({
			id: yup.string().notRequired(),
			biomassTypeId: yup.string().nullable().required('required'),
			fieldSize: yup
				.number()
				.nullable()
				.required()
				.typeError('field size is required')
				.moreThan(0, 'field size must be greater than 0'),
			fieldSizeUnit: yup.string().nullable().required(),
			biomassQuantity: yup
				.number()
				.required()
				.nullable()
				.typeError('Biomass Quantity is required')
				.moreThan(0, 'Biomass Quantity must be greater than 0'),
			documentIds: yup.array().of(
				yup
					.object()
					.shape({
						id: yup.string().nullable(),
						url: yup.string().nullable(),
						fileName: yup.string().nullable(),
					})
					.notRequired()
			),
		})
	),
})

export type TAddBiomassreference = yup.InferType<
	typeof addBiomassReferenceSchema
>
