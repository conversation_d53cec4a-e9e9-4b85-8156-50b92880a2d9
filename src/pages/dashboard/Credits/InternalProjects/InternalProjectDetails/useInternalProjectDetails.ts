import { authAxios } from '@/contexts'
import {
	IInternalProjectDetails,
	IProcessForInternalProject,
	ISinkResponse,
	IStockListResponse,
	KilnProcessList,
} from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { GridEventListener } from '@mui/x-data-grid'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import moment from 'moment'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { StockStatusType } from './InternalProjectDetails'

export const useInternalProjectDetails = () => {
	const { projectId } = useParams()
	const [searchParams, setSearchParams] = useSearchParams()
	const [showSinkFileDetails, setShowSinkFileDetails] = useState<string | null>(
		null
	)
	const [
		showDeleteStockConfirmationDialog,
		setShowDeleteStockConfirmationDialog,
	] = useState<{ id: string; status: StockStatusType } | null>(null)
	const [
		showDeleteStockSinkFileConfirmationDialog,
		setShowDeleteStockSinkFileConfirmationDialog,
	] = useState<string | null>(null)
	const [openEditDrawer, setOpenEditDrawer] =
		useState<IInternalProjectDetails | null>(null)
	const navigate = useNavigate()
	const paramsTab = searchParams.get('tab') || 'process'
	const paramsPage = searchParams.get('page') || defaultPage
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsSite = searchParams.get('site') || ''
	const [selectedRowParams, setSelectedRowParams] = useState<any>()
	const paramsStartDate =
		searchParams.get('startDate') === null
			? ''
			: moment(
					moment(searchParams.get('startDate')).format('DD-MM-YYYY')
			  ).format('YYYY-MM-DD')
	const paramsEndDate =
		searchParams.get('endDate') === null
			? ''
			: moment(moment(searchParams.get('endDate')).format('DD-MM-YYYY')).format(
					'YYYY-MM-DD'
			  )
	const paramsCrop = searchParams.get('crop') || ''
	const paramsStockCreated =
		searchParams.get('stockCreated') === 'true' ||
		searchParams.get('stockCreated') === null
			? 'false'
			: 'true'
	const [rowCount, setRowCount] = useState<number>(0)
	const [selectedIdsForProcessTab, setSelectedIdsForProcessTab] = useState<
		string[]
	>([])

	const getInternalProjectDetails = useQuery({
		queryKey: ['internalProjectDetails', projectId],
		queryFn: async () =>
			authAxios<IInternalProjectDetails>(`/internal-project/${projectId}`),
	})

	const getRowParams = (params: any) => setSelectedRowParams(params)

	const getSinkList = useQuery({
		queryKey: ['sinkList'],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<ISinkResponse>(
					`/internal-project/${projectId}/sinks?limit=${paramsLimit}&page=${paramsPage}`
				)
				setRowCount(data?.count || 0)
				return data?.sinks
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: paramsTab === 'sinks',
	})

	const getProcessList = useQuery({
		queryKey: [
			'processListForInternalProject',
			projectId,
			paramsLimit,
			paramsPage,
			paramsSite,
			paramsStartDate,
			paramsEndDate,
			paramsCrop,
			paramsStockCreated,
		],
		queryFn: async () => {
			const { data } = await authAxios.get<IProcessForInternalProject>(
				`/internal-project/${projectId}/kiln-process?limit=${paramsLimit}&page=${paramsPage}&siteId=${paramsSite}&startDate=${paramsStartDate}&endDate=${paramsEndDate}&cropId=${paramsCrop}&isStockNotCreated=${paramsStockCreated}`
			)
			setRowCount(data?.count ?? 0)
			return data?.process
		},
		enabled: paramsTab === 'process',
	})

	const getSiteList = useQuery({
		queryKey: ['siteListForInternalProject', projectId],
		queryFn: async () =>
			await authAxios.get<{ id: string; name: string }[]>(
				`/internal-project/${projectId}/site`
			),
		enabled: paramsTab === 'process',
	})

	const getStocksList = useQuery({
		queryKey: ['stockList', projectId, paramsLimit, paramsPage],
		queryFn: async () => {
			const { data } = await authAxios<IStockListResponse>(
				`/internal-project/${projectId}/stocks?limit=${paramsLimit}&page=${paramsPage}`
			)
			setRowCount(data?.count ?? 0)
			return data?.stocks
		},
		select: (data) => {
			return (data ?? [])?.map((stock, idx) => ({
				...stock,
				id: idx,
			}))
		},
		enabled: paramsTab === 'stocks',
	})

	const useGetKilnProcessList = useMutation({
		mutationKey: ['kilnProcessList'],
		mutationFn: async (stockId: string) => {
			const { data } = await authAxios.get<KilnProcessList>(
				`/stock/${stockId}/kiln-process`
			)
			return data
		},
		onError: (error) => {
			toast(error.message)
		},
	})

	const getCropList = useQuery({
		queryKey: ['cropListForInternalProject', projectId],
		queryFn: async () =>
			await authAxios.get<{ id: string; name: string }[]>(
				`/internal-project/${projectId}/crops`
			),
		enabled: paramsTab === 'process',
	})

	const createStockMutation = useMutation({
		mutationKey: ['createSelectedStock', selectedIdsForProcessTab, projectId],
		mutationFn: async () => {
			const { data } = await authAxios.post(`/global-csink/process/stock`, {
				processIds: selectedIdsForProcessTab,
				internalProjectId: projectId,
			})
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setSelectedIdsForProcessTab([])
			getProcessList?.refetch()
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})
	const handleDeleteSinkFileMutation = useMutation({
		mutationKey: ['deleteStockSinkFileMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(
				`/global-csink/sink/${showSinkFileDetails}/file/${id}`
			)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			getSinkList.refetch()
			setShowDeleteStockSinkFileConfirmationDialog(null)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const useStockOrSinkStatusMutation = () => {
		return useMutation({
			mutationKey: ['updateStockStatus'],
			mutationFn: async (type: 'stock' | 'sink') => {
				const { data } = await authAxios.put<{ message: string }>(
					`/global-csink/${type}/status`
				)
				return data
			},
			onSuccess: (data, type) => {
				if (type === 'stock') getStocksList.refetch()
				if (type === 'sink') getSinkList.refetch()
				toast.success(data?.message || `${type} status updated successfully!`)
			},
			onError: (error: AxiosError) => {
				toast(
					(error?.response?.data as { messageToUser: string })?.messageToUser
				)
			},
		})
	}

	const showSinkFileDetailsMemo = useMemo(
		() => getSinkList?.data?.find((i) => showSinkFileDetails === i?.id)?.files,
		[getSinkList?.data, showSinkFileDetails]
	)

	const handleDeleteStockMutation = useMutation({
		mutationKey: ['deleteStockMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(`/global-csink/stock/${id}`)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			getStocksList.refetch()
			setShowDeleteStockConfirmationDialog(null)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const handleCreateMutation = useCallback(() => {
		createStockMutation.mutate()
	}, [createStockMutation])

	const handleTabChange = useCallback(
		(value: string) => {
			setSearchParams(
				(prev) => {
					prev.set('tab', value)
					return prev
				},
				{ replace: true }
			)
			setRowCount(0)
		},
		[setSearchParams]
	)
	const handleBack = () => {
		navigate(-1)
	}

	const handleSelectProcess = useCallback(
		(id: string) => {
			if (selectedIdsForProcessTab?.includes(id)) {
				setSelectedIdsForProcessTab((prev) => prev.filter((x) => x !== id))
				return
			}
			setSelectedIdsForProcessTab((prev) => [...prev, id])
		},
		[selectedIdsForProcessTab]
	)
	const handleSinkRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			if (params?.row?.files?.length > 0) {
				setShowSinkFileDetails(params?.row?.id)
			}
		},
		[]
	)

	return {
		getInternalProjectDetails,
		internalProjectDetails: getInternalProjectDetails?.data?.data,
		navigate,
		paramsTab,
		handleBack,
		handleTabChange,
		paramsLimit,
		paramsPage,
		getSinkList,
		rowCount,
		getSiteList,
		getProcessList,
		getStocksList,
		getCropList,
		handleSelectProcess,
		selectedIdsForProcessTab,
		handleCreateMutation,
		createStockMutation,
		getRowParams,
		showSinkFileDetails,
		setShowSinkFileDetails,
		handleSinkRowClick,
		selectedRowParams,
		openEditDrawer,
		showDeleteStockConfirmationDialog,
		setShowDeleteStockConfirmationDialog,
		showDeleteStockSinkFileConfirmationDialog,
		setShowDeleteStockSinkFileConfirmationDialog,
		setOpenEditDrawer,
		handleDeleteStockMutation,
		handleDeleteSinkFileMutation,
		showSinkFileDetailsMemo,
		useGetKilnProcessList,
		useStockOrSinkStatusMutation,
	}
}
