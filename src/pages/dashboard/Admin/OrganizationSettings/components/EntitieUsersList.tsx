import {
	ActionInformationDrawer,
	CustomCard,
	CustomChip,
	MediaCarousal,
} from '@/components'
import { Close } from '@mui/icons-material'
import { Box, Chip, CircularProgress, styled, Typography } from '@mui/material'
import { Stack, IconButton } from '@mui/material'
import { theme } from '@/lib/theme/theme'
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import {
	SetURLSearchParams,
	useNavigate,
	useSearchParams,
} from 'react-router-dom'
import EditIcon from '@/assets/icons/editIcon.svg'
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline'
import { authAxios, useAuthContext } from '@/contexts'
import {
	entitiesRoles,
	EntityTabEnum,
	FieldTypeEnum,
	userRoles,
} from '@/utils/constant'
import { useCallback, useMemo, useState } from 'react'
import { downloadKMLFileForEntities } from '@/utils/helper/generateKML'
import {
	IFileData,
	ILocation,
	IMediaWithDeleteParams,
	User,
} from '@/interfaces'
import {
	convertNetworkManagerToUserType,
	convertToUserType,
} from '../useOrganizationSettings'
import { StrategyContent } from './StrategyContent'
import { IArtisanProDetails } from '@/interfaces/artisanProNetworkDetails.type'
import {
	capitalizeFirstLetter,
	handleViewKMLFile,
	showAxiosErrorToast,
} from '@/utils/helper'
import { Confirmation } from '@/components/Confirmation/Confirmation'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { toast } from 'react-toastify'
import { AssignTypesDialog } from './AssignTypesDialog'
import { EntityEnum } from '@/interfaces'
import { GoogleMapsDraw } from '@/components/GoogleMap'
import { TabContext, TabPanel } from '@mui/lab'
import { UsersItem } from './UsersItem'

interface EntityUsersListProps {
	isEntityUsersLoading: boolean
	setShowAssignUserDialog: (value: boolean) => void
	entityUsers?: IArtisanProDetails
	totalEntityUsersCount: number
	canEditOrDeleteUser: (value: userRoles, isEditButton: boolean) => boolean
	setShowEditUser: React.Dispatch<React.SetStateAction<User | null>>
	handleOpenDetails: (value: User) => void
	setShowBiomassReferenceDialog: React.Dispatch<
		React.SetStateAction<{
			id: string
			type: EntityEnum
		} | null>
	>
	handleDeleteUser: (user: User | null) => void
}

interface IPassedProps {
	entityUsers?: IArtisanProDetails
	setEditItem: React.Dispatch<React.SetStateAction<boolean>>
	setShowAddOrEdit: React.Dispatch<React.SetStateAction<boolean>>
	setShowMethaneStrategy: React.Dispatch<React.SetStateAction<boolean>>
	setShowBiomassReferenceDialog: React.Dispatch<
		React.SetStateAction<{
			id: string
			type: EntityEnum
		} | null>
	>
	isCSinkOrArtisan: boolean
	setShowBiomassPreProcessingStrategy: React.Dispatch<
		React.SetStateAction<boolean>
	>
	entityType: string
	entityId: string
}

interface IPassedPropsDetailsType {
	setSearchParams: SetURLSearchParams
	setShowMap: React.Dispatch<React.SetStateAction<boolean>>
	showMethaneStrategy: boolean
	setShowMethaneStrategy: React.Dispatch<React.SetStateAction<boolean>>
	editItem: boolean
	showAddOrEdit: boolean
	setShowAddOrEdit: React.Dispatch<React.SetStateAction<boolean>>
	setEditItem: React.Dispatch<React.SetStateAction<boolean>>
	showMap: boolean
	searchParams: URLSearchParams
	entityUsers?: IArtisanProDetails
	setShowBiomassReferenceDialog: React.Dispatch<
		React.SetStateAction<{
			id: string
			type: EntityEnum
		} | null>
	>
	setMixingDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
	setApplicationDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
	setBiomassDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
	entityType: string
	entityId: string
	isCSinkOrArtisan: boolean
	showBiomassPreProcessingStrategy: boolean
	setShowBiomassPreProcessingStrategy: React.Dispatch<
		React.SetStateAction<boolean>
	>
}

enum entityDetailsTabs {
	setting = 'Settings',
	users = 'Users',
}

const tabs = [
	{
		label: 'Settings',
		value: entityDetailsTabs.setting,
		hidden: false,
	},
	{
		label: 'Users',
		value: entityDetailsTabs.users,
		hidden: false,
	},
]

export const EntityUsersList = ({
	isEntityUsersLoading,
	setShowAssignUserDialog,
	entityUsers,
	canEditOrDeleteUser,
	setShowEditUser,
	handleOpenDetails,
	handleDeleteUser,
	setShowBiomassReferenceDialog,
}: EntityUsersListProps) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsTab = searchParams.get('tab') ?? entityDetailsTabs.setting
	const [showMap, setShowMap] = useState<boolean>(false)
	const [editItem, setEditItem] = useState(false)
	const [showAddOrEdit, setShowAddOrEdit] = useState<boolean>(false)
	const [showMethaneStrategy, setShowMethaneStrategy] = useState(false)
	const [mixingDialogOpen, setMixingDialogOpen] = useState(false)
	const [applicationDialogOpen, setApplicationDialogOpen] = useState(false)
	const [biomassDialogOpen, setBiomassDialogOpen] = useState(false)
	const assignDialog = [
		{
			field: FieldTypeEnum.mixingTypes,
			dialog: mixingDialogOpen,
			setDialogOpen: setMixingDialogOpen,
		},
		{
			field: FieldTypeEnum.applicationTypes,
			dialog: applicationDialogOpen,
			setDialogOpen: setApplicationDialogOpen,
		},
		{
			field: FieldTypeEnum.assignedBiomass,
			dialog: biomassDialogOpen,
			setDialogOpen: setBiomassDialogOpen,
		},
	]
	const entityType = searchParams.get('selectedEntityType') || ''
	const entityId = searchParams.get('entityId') || ''
	const handleTabChange = useCallback(
		(newValue: entityDetailsTabs) => {
			setSearchParams(
				(prev) => {
					const params = new URLSearchParams(prev)

					// update or add your key
					params.set('tab', newValue)

					return params
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleCloseSelectedEntity = useCallback(() => {
		setSearchParams(
			(prev) => {
				const params = new URLSearchParams(prev)

				// update or add your key
				params.delete('tab')
				params.delete('entityId')
				params.delete('selectedEntityType')

				return params
			},
			{ replace: true }
		)
	}, [setSearchParams])

	const isCSinkOrArtisan = useMemo(() => {
		return (
			entityType === entitiesRoles.cSinkNetwork ||
			entityType === entitiesRoles.ArtisanPro
		)
	}, [entityType])

	const [
		showBiomassPreProcessingStrategy,
		setShowBiomassPreProcessingStrategy,
	] = useState(false)

	const allUsers = useMemo(
		() => [
			...(entityUsers?.managerDetails?.map((user) =>
				convertNetworkManagerToUserType(
					user,
					entityId,
					entityUsers?.name,
					entityUsers?.csinkManagerName
				)
			) || []),
			...(entityUsers?.operatorDetails?.map((user) =>
				convertToUserType(
					user,
					entityId,
					entityUsers.name,
					entityUsers.csinkManagerName
				)
			) || []),
			...(entityUsers?.farmers?.map((user) =>
				convertToUserType(
					user,
					entityId,
					entityUsers.name,
					entityUsers.csinkManagerName
				)
			) || []),
		],
		[
			entityId,
			entityUsers?.csinkManagerName,
			entityUsers?.farmers,
			entityUsers?.managerDetails,
			entityUsers?.name,
			entityUsers?.operatorDetails,
		]
	)

	return (
		<>
			<Box className='user-box-entity-list'>
				<StyledContainer>
					{isEntityUsersLoading ? (
						<Stack alignItems={'center'}>
							<Box
								component={CircularProgress}
								alignSelf='center'
								mt={theme.spacing(2)}
							/>
						</Stack>
					) : (
						<TabContext value={paramsTab}>
							<Stack
								direction={'row'}
								justifyContent={'space-between'}
								padding={theme.spacing(1, 2)}>
								<Stack
									direction={'row'}
									gap={theme.spacing(1)}
									alignItems={'center'}>
									{tabs.map(
										({ label, value, hidden }, index) =>
											!hidden && (
												<Chip
													key={value + index}
													label={label}
													color={paramsTab === value ? 'primary' : 'default'}
													onClick={() => handleTabChange(value)}
												/>
											)
									)}
								</Stack>
								<Stack
									direction={'row'}
									gap={theme.spacing(1)}
									alignItems={'center'}>
									{paramsTab === entityDetailsTabs.users ? (
										<Chip
											label='Assign User'
											color='primary'
											onClick={() => setShowAssignUserDialog(true)}
										/>
									) : null}
									<IconButton
										onClick={handleCloseSelectedEntity}
										sx={{
											color: theme.palette.grey[500],
										}}>
										<Close fontSize='small' />
									</IconButton>
								</Stack>
							</Stack>
							<TabPanel value={entityDetailsTabs.setting} sx={{ padding: 0 }}>
								<DetailsType
									entityUsers={entityUsers}
									setSearchParams={setSearchParams}
									searchParams={searchParams}
									setShowBiomassReferenceDialog={setShowBiomassReferenceDialog}
									setShowMap={setShowMap}
									showMap={showMap}
									editItem={editItem}
									setEditItem={setEditItem}
									showAddOrEdit={showAddOrEdit}
									setShowAddOrEdit={setShowAddOrEdit}
									showMethaneStrategy={showMethaneStrategy}
									setShowMethaneStrategy={setShowMethaneStrategy}
									setMixingDialogOpen={setMixingDialogOpen}
									setApplicationDialogOpen={setApplicationDialogOpen}
									setBiomassDialogOpen={setBiomassDialogOpen}
									entityType={entityType}
									entityId={entityId}
									isCSinkOrArtisan={isCSinkOrArtisan}
									showBiomassPreProcessingStrategy={
										showBiomassPreProcessingStrategy
									}
									setShowBiomassPreProcessingStrategy={
										setShowBiomassPreProcessingStrategy
									}
								/>
							</TabPanel>
							<TabPanel value={entityDetailsTabs.users} sx={{ padding: 0 }}>
								<Stack gap={theme.spacing(1)} padding={theme.spacing(1)}>
									<Stack gap={theme.spacing(1)}>
										{allUsers.map((user) => (
											<UsersItem
												key={user.id}
												item={user}
												canEditOrDeleteUser={canEditOrDeleteUser}
												setShowEditUser={setShowEditUser}
												handleOpenDetails={handleOpenDetails}
												handleDeleteUser={handleDeleteUser}
											/>
										))}
									</Stack>
								</Stack>
							</TabPanel>
						</TabContext>
					)}
				</StyledContainer>
			</Box>
			{assignDialog.map((val) => (
				<AssignTypesDialog
					key={val.field}
					open={val.dialog}
					onClose={() => val.setDialogOpen(false)}
					entityId={entityUsers?.id || ''}
					field={val.field as FieldTypeEnum}
					assignedValues={entityUsers?.[val.field as FieldTypeEnum] ?? []}
					csinkManagerId={entityUsers?.csinkManagerId ?? entityUsers?.id}
				/>
			))}
		</>
	)
}

interface TypeDetailsConfig {
	type: string
	btnLabel: string
	field: FieldTypeEnum
}
function convertNetworkType(networkType: string): EntityEnum {
	switch (networkType) {
		case 'artisan_pro':
			return EntityEnum.aps
		case 'csink_network':
			return EntityEnum.cSinkNetwork
		default:
			return EntityEnum.cSinkManager
	}
}

const typeDetailsConfig: TypeDetailsConfig[] = [
	{
		type: 'Mixing Type',
		btnLabel: 'No Mixing type assigned to this network.',
		field: FieldTypeEnum.mixingTypes,
	},
	{
		type: 'Application Type',
		btnLabel: 'No Application type assigned to this network.',
		field: FieldTypeEnum.applicationTypes,
	},
	{
		type: 'Assigned Biomass',
		btnLabel: 'No Biomass assigned to this network.',
		field: FieldTypeEnum.assignedBiomass,
	},
]

const DetailsType = ({
	entityUsers,
	setShowBiomassReferenceDialog,
	setSearchParams,
	searchParams,
	setShowMap,
	showMap,
	editItem,
	setEditItem,
	showAddOrEdit,
	setShowAddOrEdit,
	showMethaneStrategy,
	setShowMethaneStrategy,
	setMixingDialogOpen,
	setApplicationDialogOpen,
	setBiomassDialogOpen,
	entityType,
	entityId,
	isCSinkOrArtisan,
	showBiomassPreProcessingStrategy,
	setShowBiomassPreProcessingStrategy,
}: IPassedPropsDetailsType) => {
	const queryClient = useQueryClient()
	const navigate = useNavigate()

	const [selectedMethaneCompensateId, setSelectedMethaneCompensateId] =
		useState<string>('')
	const { userDetails } = useAuthContext()
	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])

	const isAdminOrCsinkManager = useMemo(() => {
		return (
			userDetails?.accountType === userRoles.Admin ||
			userDetails?.accountType === userRoles.CsinkManager
		)
	}, [userDetails?.accountType])
	const premTypes = useMemo(() => {
		const accountType = userDetails?.accountType as userRoles
		if (!accountType) return false

		const isCsinkManagerUser =
			accountType === userRoles.CsinkManager &&
			entityType !== entitiesRoles.CsinkManager

		const isAdminUser = accountType === userRoles.Admin

		const isPermittedForCsinkManagerEntity =
			entityType === entitiesRoles.CsinkManager &&
			![
				userRoles.CsinkManager,
				userRoles.ArtisanPro,
				userRoles.cSinkNetwork,
				userRoles.Manager,
			].includes(accountType)

		const isPermittedForNetworkOrProEntity =
			(entityType === entitiesRoles.cSinkNetwork ||
				entityType === entitiesRoles.ArtisanPro) &&
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.cSinkNetwork,
				userRoles.ArtisanPro,
				userRoles.Manager,
			].includes(accountType)

		return (
			isCsinkManagerUser ||
			isAdminUser ||
			isPermittedForCsinkManagerEntity ||
			isPermittedForNetworkOrProEntity
		)
	}, [entityType, userDetails?.accountType])
	const [selectedBiomassType, setSelectedBiomassType] = useState('')
	const [methaneDeleteId, setMethaneDeleteId] = useState<string | null>(null)
	const [showMediaModal, setShowMediaModal] = useState<IFileData[] | null>(null)

	const deleteMethaneCompensationMutation = useMutation({
		mutationKey: ['deleteMethaneCompensationMutation'],
		mutationFn: async (id: string) => {
			const api =
				entityType === entitiesRoles.cSinkNetwork
					? `/cs-network/${entityUsers?.id}/methane-compensate-strategy/${id}` //delete for csink
					: `/artisian-pro/${entityUsers?.id}/methane-compensate-strategy/${id}` //delete for artisan
			const { data } = await authAxios.delete(api)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.invalidateQueries({
				queryKey: [
					entityType === entitiesRoles.cSinkNetwork
						? 'cSinkNetworkDetails'
						: 'artisanProDetail',
				],
			})
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
		},
	})

	const handleDelete = useCallback(() => {
		if (!methaneDeleteId) return
		deleteMethaneCompensationMutation.mutate(methaneDeleteId)
	}, [deleteMethaneCompensationMutation, methaneDeleteId])

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const networkId = searchParams.get('networkId')
			if (!networkId) {
				toast.error('Network ID is missing, cannot save KML file.')
				throw new Error('NetworkId is missing')
			}

			const payload = {
				kmlCoordinates: mapData,
			}

			const api =
				entityType === entitiesRoles.ArtisanPro
					? `/artisian-pro/${networkId}/kml-coordinates`
					: `/cs-network/${networkId}/kml-coordinates`

			await authAxios.put(api, payload)

			return mapData
		},
		onSuccess: (data: ILocation[]) => {
			if (!data || data.length === 0) {
				toast('No KML file is added.')
			} else {
				toast('KML file is added.')
			}

			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
		},
		onError: (error: unknown) => {
			if (error instanceof AxiosError) {
				showAxiosErrorToast(error)
			} else if (error instanceof Error) {
				toast.error(error.message)
			}
		},
	})

	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)

	const handleCloseMethaneStrategy = () => {
		setShowAddOrEdit(false)
		setShowMethaneStrategy(false)
		setSelectedMethaneCompensateId('')
	}

	return (
		<StyledContainer>
			<Stack
				gap={theme.spacing(2)}
				paddingX={theme.spacing(2)}
				sx={{
					display: 'grid',
					gridTemplateColumns: { sm: '1fr 1fr' },
				}}>
				{typeDetailsConfig.map((config) => (
					<TypeDetails
						key={config.field}
						type={config.type}
						btnLabel={config.btnLabel}
						entityUsers={entityUsers}
						field={config.field}
						premTypes={premTypes}
						setMixingDialogOpen={setMixingDialogOpen}
						setApplicationDialogOpen={setApplicationDialogOpen}
						setBiomassDialogOpen={setBiomassDialogOpen}
					/>
				))}
				{isCSinkOrArtisan ? (
					<CustomCard
						sx={{
							padding: 0,
							borderRadius: theme.spacing(2),
						}}
						headerComponent={
							<Stack>
								<Box
									display='flex'
									justifyContent='space-between'
									alignItems='center'
									p={theme.spacing(1, 2)}>
									<Typography
										variant='subtitle1'
										fontWeight={theme.typography.caption.fontWeight}>
										KML File
									</Typography>
									<Box
										sx={{ cursor: 'pointer' }}
										onClick={(e) => {
											e.stopPropagation()
											setSearchParams(
												(urlParams) => {
													urlParams.set('networkId', entityUsers?.id ?? '')
													urlParams.set('lat', '0')
													urlParams.set('long', '0')
													return urlParams
												},
												{ replace: true }
											)
											setShowMap(true)
										}}>
										<CustomChip
											isSmall
											appliedClass='addAssignClass'
											label='Add'
										/>
									</Box>
								</Box>
								{entityUsers?.kmlCoordinates ? (
									<Box
										className='file-box'
										onClick={() => {
											const firstPoint = entityUsers?.kmlCoordinates?.[0]
											handleViewKMLFile({
												center: {
													x: String(firstPoint?.x ?? '0'),
													y: String(firstPoint?.y ?? '0'),
												},
												farmCoordinates: entityUsers?.kmlCoordinates ?? [],
												navigate,
												networkId: entityUsers?.id ?? '',
												setFarmCoordinates,
												setShowMap: () => setShowMap(true),
											})
										}}>
										<Box className='file-info'>
											<DescriptionOutlinedIcon
												sx={{
													fontSize: theme.spacing(2.5),
													color: theme.palette.grey[400],
												}}
											/>
											<Typography variant='subtitle1' color='text.primary'>
												{entityUsers?.address
													? `${entityUsers.address}.kml`
													: 'location.kml'}
											</Typography>
										</Box>
										<IconButton
											edge='end'
											onClick={(e) => {
												e.stopPropagation()
												downloadKMLFileForEntities(
													entityUsers?.kmlCoordinates,
													entityUsers?.address
												)
											}}>
											<FileDownloadOutlinedIcon
												sx={{
													fontSize: theme.spacing(2.5),
													color: theme.palette.grey[400],
												}}
											/>
										</IconButton>
									</Box>
								) : (
									<Typography
										fontSize={theme.spacing(1.5)}
										textAlign={'center'}>
										No KML is added to this network.
									</Typography>
								)}
							</Stack>
						}
					/>
				) : null}
				<AddList
					entityUsers={entityUsers}
					setEditItem={setEditItem}
					setShowAddOrEdit={setShowAddOrEdit}
					setShowMethaneStrategy={setShowMethaneStrategy}
					setShowBiomassReferenceDialog={setShowBiomassReferenceDialog}
					isCSinkOrArtisan={isCSinkOrArtisan}
					setShowBiomassPreProcessingStrategy={
						setShowBiomassPreProcessingStrategy
					}
					entityId={entityId}
					entityType={entityType}
				/>
				{isCSinkOrArtisan &&
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.ArtisanPro,
						userRoles.cSinkNetwork,
						userRoles.Manager,
					].includes(userDetails?.accountType as userRoles) && (
						<>
							{entityUsers?.methaneCompensateStrategies &&
								entityUsers?.methaneCompensateStrategies.length > 0 && (
									<Stack
										sx={{
											paddingTop: theme.spacing(1),
											gridColumn: '1 / -1',
										}}
										spacing={1}>
										<Stack direction={'row'} gap={theme.spacing(2)}>
											<Typography
												variant='body2'
												fontWeight={theme.typography.caption.fontWeight}>
												Methane Compensate Strategy
											</Typography>
											{isAdminOrCsinkManager ? (
												<CustomChip
													appliedClass='addAssignClass'
													onClick={() => {
														setEditItem(false)
														setShowAddOrEdit(true)
														setShowMethaneStrategy(true)
													}}
													label='Add'
													isSmall
												/>
											) : null}
										</Stack>
										<Stack
											gap={theme.spacing(2)}
											sx={{
												display: 'grid',
												gridTemplateColumns: { sm: '1fr 1fr' },
											}}>
											{entityUsers?.methaneCompensateStrategies?.map(
												(methaneCompensate) => (
													<CustomCard
														onClick={() => {
															setEditItem(false)
															setShowMethaneStrategy(true)
															setSelectedMethaneCompensateId(
																methaneCompensate.id as string
															)
														}}
														sx={{
															padding: 0,
															cursor: 'pointer',
															borderRadius: theme.spacing(2),
														}}
														key={methaneCompensate.id}
														headerComponent={
															<Stack
																padding={theme.spacing(1)}
																gap={theme.spacing(0.5)}>
																<Stack
																	direction='row'
																	alignItems='center'
																	justifyContent='space-between'>
																	<Stack
																		spacing={theme.spacing(1)}
																		padding={theme.spacing(0.5)}
																		direction='row'
																		alignItems='center'>
																		<Typography variant='subtitle1'>
																			{methaneCompensate.biomassName}
																		</Typography>
																		{methaneCompensate?.methaneCompensateType && (
																			<CustomChip
																				isSmall
																				label={capitalizeFirstLetter(
																					methaneCompensate.methaneCompensateType
																				)
																					.split('_')
																					.join(' ')}
																				appliedClass='transparent'
																			/>
																		)}
																	</Stack>
																	<Stack
																		spacing={1}
																		direction='row'
																		alignItems='center'>
																		{isAdminOrCsinkManager &&
																			isCSinkOrArtisan && (
																				<>
																					<Box
																						component='img'
																						src={EditIcon}
																						className='filter-bsi'
																						width={theme.spacing(2)}
																						sx={{
																							cursor: 'pointer',
																						}}
																						onClick={(e) => {
																							e.stopPropagation()
																							setShowMethaneStrategy(true)
																							setShowAddOrEdit(true)
																							setEditItem(true)
																							setSelectedMethaneCompensateId(
																								methaneCompensate.id as string
																							)
																						}}
																					/>
																					<IconButton
																						size='small'
																						sx={{
																							color: 'primary.main',
																							cursor: 'pointer',
																						}}
																						onClick={(e) => {
																							e.stopPropagation()
																							setMethaneDeleteId(
																								methaneCompensate.id as string
																							)
																						}}>
																						<DeleteOutlineIcon
																							sx={{
																								fontSize: theme.spacing(2.5),
																							}}
																						/>
																					</IconButton>
																				</>
																			)}
																	</Stack>
																</Stack>
																<Stack
																	paddingX={theme.spacing(0.5)}
																	direction={'row'}
																	alignItems={'center'}
																	justifyContent={'space-between'}>
																	<Typography
																		fontSize={theme.spacing(1.5)}
																		variant='subtitle1'
																		sx={{
																			maxWidth: theme.spacing(25),
																			whiteSpace: 'nowrap',
																			overflow: 'hidden',
																			textOverflow: 'ellipsis',
																		}}>
																		{methaneCompensate.description}
																	</Typography>
																	<IconButton
																		size='small'
																		sx={{
																			cursor: 'pointer',
																		}}
																		onClick={(e) => {
																			e.stopPropagation()
																			setShowMediaModal(
																				methaneCompensate?.documents || null
																			)
																		}}>
																		<DescriptionOutlinedIcon
																			sx={{
																				fontSize: theme.spacing(2.5),
																				color: theme.palette.grey[400],
																			}}
																		/>
																	</IconButton>
																</Stack>
															</Stack>
														}
													/>
												)
											)}
										</Stack>
									</Stack>
								)}
							<Confirmation
								confirmationText='Are you sure you want to delete this Methane Compensate Strategy?'
								open={!!methaneDeleteId}
								handleClose={() => setMethaneDeleteId(null)}
								handleNoClick={() => setMethaneDeleteId(null)}
								handleYesClick={() => {
									handleDelete()
									setMethaneDeleteId(null)
								}}
							/>
							{showMethaneStrategy ? (
								<ActionInformationDrawer
									open={showMethaneStrategy}
									onClose={handleCloseMethaneStrategy}
									anchor='right'
									component={
										<StrategyContent
											editItem={editItem}
											setEditItem={setEditItem}
											selectedMethaneCompensateId={selectedMethaneCompensateId}
											handleClose={handleCloseMethaneStrategy}
											type={EntityTabEnum.methaneCompensationStrategy}
											entityUserId={entityUsers?.id}
											addOrEdit={showAddOrEdit}
										/>
									}
								/>
							) : null}
							{entityUsers?.biomassPreprocessingDetails &&
								Object.values(entityUsers?.biomassPreprocessingDetails).some(
									(value) => Boolean(value) === true
								) && (
									<Stack
										sx={{
											gridColumn: '1 / -1',
											paddingTop: theme.spacing(1),
										}}
										spacing={theme.spacing(1)}>
										<Stack
											direction='row'
											alignItems='center'
											gap={theme.spacing(1)}>
											<Typography
												variant='body2'
												fontWeight={theme.typography.caption.fontWeight}>
												Biomass Pre-Processing
											</Typography>
											{isAdminOrCsinkManager && isCSinkOrArtisan && (
												<Box
													component='img'
													src={EditIcon}
													className='filter-bsi'
													width={theme.spacing(2)}
													onClick={() => {
														setShowBiomassPreProcessingStrategy(true)
														setEditItem(true)
													}}
												/>
											)}
										</Stack>
										<Stack
											gap={theme.spacing(2)}
											sx={{
												display: 'grid',
												gridTemplateColumns: { sm: '1fr 1fr' },
											}}>
											{entityUsers?.biomassPreprocessingDetails
												?.dryingStrategy && (
												<CustomCard
													sx={{
														cursor: 'pointer',
														borderRadius: theme.spacing(2),
														padding: 0,
													}}
													onClick={() => {
														setShowBiomassPreProcessingStrategy(true)
														setEditItem(false)
														setSelectedBiomassType('drying')
													}}
													headerComponent={
														<Stack
															padding={theme.spacing(1)}
															gap={theme.spacing(0.5)}>
															<Stack
																direction='row'
																alignItems='center'
																justifyContent='space-between'
																paddingX={theme.spacing(0.5)}>
																<Typography variant='subtitle1'>
																	Drying{' '}
																	<CustomChip
																		isSmall
																		label={capitalizeFirstLetter(
																			entityUsers?.biomassPreprocessingDetails
																				?.dryingType
																		)
																			.split('_')
																			.join(' ')}
																		appliedClass='transparent'
																	/>
																</Typography>
															</Stack>
															<Typography
																fontSize={theme.spacing(1.5)}
																variant='subtitle1'
																paddingX={theme.spacing(0.5)}
																sx={{
																	maxWidth: theme.spacing(25),
																	whiteSpace: 'nowrap',
																	overflow: 'hidden',
																	textOverflow: 'ellipsis',
																}}>
																{
																	entityUsers?.biomassPreprocessingDetails
																		?.dryingStrategy
																}
															</Typography>
														</Stack>
													}
												/>
											)}

											{entityUsers?.biomassPreprocessingDetails
												?.shreddingStrategy && (
												<CustomCard
													sx={{
														cursor: 'pointer',
														borderRadius: theme.spacing(2),
														padding: 0,
													}}
													onClick={() => {
														setShowBiomassPreProcessingStrategy(true)
														setEditItem(false)
														setSelectedBiomassType('shredding')
													}}
													headerComponent={
														<Stack
															padding={theme.spacing(1)}
															gap={theme.spacing(0.5)}>
															<Stack
																direction='row'
																alignItems='center'
																justifyContent='space-between'
																paddingX={theme.spacing(0.5)}>
																<Typography variant='subtitle1'>
																	Shredding{' '}
																	<CustomChip
																		isSmall
																		label={capitalizeFirstLetter(
																			entityUsers?.biomassPreprocessingDetails
																				?.shreddingType
																		)
																			.split('_')
																			.join(' ')}
																		appliedClass='transparent'
																	/>
																</Typography>
															</Stack>
															<Typography
																fontSize={theme.spacing(1.5)}
																variant='subtitle1'
																paddingX={theme.spacing(0.5)}
																sx={{
																	maxWidth: theme.spacing(25),
																	whiteSpace: 'nowrap',
																	overflow: 'hidden',
																	textOverflow: 'ellipsis',
																}}>
																{
																	entityUsers?.biomassPreprocessingDetails
																		?.shreddingStrategy
																}
															</Typography>
														</Stack>
													}
												/>
											)}
										</Stack>
									</Stack>
								)}
							{showBiomassPreProcessingStrategy ? (
								<ActionInformationDrawer
									open={showBiomassPreProcessingStrategy}
									onClose={() => setShowBiomassPreProcessingStrategy(false)}
									anchor='right'
									component={
										<StrategyContent
											selectedMethaneCompensateId={selectedMethaneCompensateId}
											editItem={editItem}
											setEditItem={setEditItem}
											handleClose={() =>
												setShowBiomassPreProcessingStrategy(false)
											}
											type={EntityTabEnum.biomassProcessingStrategy}
											selectedBiomassType={selectedBiomassType}
											entityUserId={entityUsers?.id}
											addOrEdit={editItem}
										/>
									}
								/>
							) : null}
						</>
					)}
				{entityUsers?.biomassReference &&
					entityUsers?.biomassReference.length > 0 && (
						<Stack spacing={theme.spacing(1)}>
							<Stack direction={'row'} justifyContent={'space-between'}>
								<Typography
									variant='subtitle1'
									fontWeight={theme.typography.caption.fontWeight}>
									Biomass Reference
								</Typography>
								<CustomChip
									isSmall
									appliedClass='addAssignClass'
									onClick={() =>
										setShowBiomassReferenceDialog({
											id: entityId,
											type: convertNetworkType(entityType),
										})
									}
									label='Add'
								/>
							</Stack>
							<CustomCard
								sx={{
									borderRadius: theme.spacing(2),
									padding: theme.spacing(1),
								}}
								headerComponent={
									<Stack gap={theme.spacing(1)}>
										{entityUsers?.biomassReference?.map((biomassRef) => (
											<CustomCard
												sx={{
													borderRadius: theme.spacing(1),
													padding: 0,
												}}
												key={biomassRef.id}
												headerComponent={
													<Stack padding={theme.spacing(1)}>
														<Typography variant='subtitle1'>
															{biomassRef.biomassName} ({biomassRef.fieldSize}{' '}
															{biomassRef.fieldSizeUnit} ={' '}
															{biomassRef.biomassQuantity}kgs)
														</Typography>
													</Stack>
												}
											/>
										))}
									</Stack>
								}
							/>
						</Stack>
					)}
			</Stack>
			{showMap ? (
				<GoogleMapsDraw
					open={showMap}
					handleModalClose={() => {
						setSearchParams((params) => {
							params.delete('lat')
							params.delete('long')
							params.delete('networkId')

							return params
						})
						setShowMap(false)
						setFarmCoordinates([])
					}}
					handleSave={handleSaveKml}
					initialPolygons={farmCoordinates}
				/>
			) : null}
			{showMediaModal ? (
				<MediaCarousal
					showDownloadButton={false}
					open={!!showMediaModal}
					onClose={() => {
						setShowMediaModal(null)
					}}
					gallery={showMediaModal as IMediaWithDeleteParams[]}
				/>
			) : null}
		</StyledContainer>
	)
}

const TypeDetails = ({
	type,
	btnLabel,
	entityUsers,
	field,
	premTypes,
	setMixingDialogOpen,
	setApplicationDialogOpen,
	setBiomassDialogOpen,
}: {
	type: string
	btnLabel: string
	entityUsers?: IArtisanProDetails
	field: FieldTypeEnum
	premTypes: boolean
	setMixingDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
	setApplicationDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
	setBiomassDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
}) => {
	const chipData = (data: any) => ({
		id: data?.id,
		name:
			field === FieldTypeEnum.assignedBiomass
				? data?.cropName
				: field === FieldTypeEnum.applicationTypes
				? data.type
				: data.name,
	})

	const handleButtonClick = useCallback(() => {
		if (field === FieldTypeEnum.mixingTypes) setMixingDialogOpen(true)
		else if (field === FieldTypeEnum.applicationTypes)
			setApplicationDialogOpen(true)
		else setBiomassDialogOpen(true)
	}, [
		field,
		setApplicationDialogOpen,
		setBiomassDialogOpen,
		setMixingDialogOpen,
	])

	return (
		<CustomCard
			sx={{
				padding: 0,
				borderRadius: theme.spacing(2),
			}}
			headerComponent={
				<Stack>
					<Box
						display='flex'
						justifyContent='space-between'
						alignItems='center'
						p={theme.spacing(1, 2)}>
						<Typography
							variant='subtitle1'
							fontWeight={theme.typography.caption.fontWeight}>
							{type}
						</Typography>
						{premTypes && (
							<CustomChip
								isSmall
								appliedClass='addAssignClass'
								onClick={handleButtonClick}
								label='Assign'
							/>
						)}
					</Box>
					{entityUsers?.[field] && entityUsers?.[field].length > 0 ? (
						<Stack direction='row' spacing={1} px={1} pb={1}>
							{entityUsers?.[field]?.slice(0, 2).map((label) => (
								<CustomChip
									isSmall
									key={chipData(label).id}
									label={chipData(label).name}
									appliedClass='transparent'
									maxWidth={theme.spacing(15)}
								/>
							))}
							{entityUsers?.[field].length > 2 && (
								<CustomChip
									isSmall
									appliedClass='transparent'
									background={theme.palette.primary.main}
									label={`+${entityUsers?.[field]?.length - 2}`}
								/>
							)}
						</Stack>
					) : (
						<Typography fontSize={theme.spacing(1.5)} textAlign={'center'}>
							{btnLabel}
						</Typography>
					)}
				</Stack>
			}
		/>
	)
}

const AddList = ({
	entityUsers,
	setEditItem,
	setShowAddOrEdit,
	setShowMethaneStrategy,
	setShowBiomassReferenceDialog,
	isCSinkOrArtisan,
	setShowBiomassPreProcessingStrategy,
	entityType,
	entityId,
}: IPassedProps) => {
	const items = [
		{
			name: 'Methane Compensation',
			show:
				!entityUsers?.methaneCompensateStrategies?.length && isCSinkOrArtisan,
			dailog: () => {
				setEditItem(false)
				setShowAddOrEdit(true)
				setShowMethaneStrategy(true)
			},
			helpText: 'No methane compensate to this network.',
		},
		{
			name: 'Biomass Pre Processing',
			show:
				isCSinkOrArtisan &&
				entityUsers?.biomassPreprocessingDetails &&
				Object.values(entityUsers?.biomassPreprocessingDetails).every(
					(value) => !value
				),
			dailog: () => {
				setShowBiomassPreProcessingStrategy(true)
				setEditItem(true)
			},
			helpText: 'No Biomass Pre-processing added to this network.',
		},
		{
			name: 'Biomass Reference',
			show: !entityUsers?.biomassReference?.length,
			dailog: () => {
				setShowBiomassReferenceDialog({
					id: entityId,
					type: convertNetworkType(entityType),
				})
			},
			helpText: 'No Biomass Reference added to this network.',
		},
	]
	return (
		<>
			{items.map((label) =>
				label.show ? (
					<CustomCard
						sx={{
							padding: 0,
							borderRadius: theme.spacing(2),
						}}
						key={label.name}
						headerComponent={
							<Stack>
								<Box
									display='flex'
									justifyContent='space-between'
									alignItems='center'
									p={theme.spacing(1, 2)}>
									<Typography
										variant='subtitle1'
										fontWeight={theme.typography.caption.fontWeight}>
										{label.name}
									</Typography>
									<CustomChip
										isSmall
										appliedClass='addAssignClass'
										onClick={() => label.dailog()}
										label='Add'
									/>
								</Box>

								<Stack direction={'column'} alignItems={'center'}>
									<Typography fontSize={theme.spacing(1.5)}>
										{label.helpText}
									</Typography>
								</Stack>
							</Stack>
						}
					/>
				) : null
			)}
		</>
	)
}

const StyledContainer = styled(Stack)(() => ({
	'.btn-transform': {
		fontWeight: theme.typography.caption.fontWeight,
		padding: 0,
		fontSize: theme.typography.subtitle1.fontSize,
	},
	'.file-box': {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'space-between',
		padding: `${theme.spacing(0)} ${theme.spacing(2)}`,
		borderRadius: theme.shape.borderRadius * 2,
		cursor: 'pointer',
		width: '100%',
	},
	'.file-info': {
		display: 'flex',
		alignItems: 'center',
		gap: theme.spacing(1),
	},
	'.filter-bsi': {
		filter: 'brightness(0) saturate(100%) invert(0%)',
		cursor: 'pointer',
	},
}))
