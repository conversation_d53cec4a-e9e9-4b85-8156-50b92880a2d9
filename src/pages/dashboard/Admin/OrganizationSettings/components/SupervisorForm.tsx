import { Add, Call, MailOutline, Remove } from '@mui/icons-material'
import {
	Box,
	Button,
	FormControl,
	FormHelperText,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Typography,
} from '@mui/material'
import { UseFormReturn } from 'react-hook-form'
import { useState } from 'react'
import {
	addSupervisorDetailsSchema,
	TAddSupervisorDetailsSchema,
} from './schema'
import { CustomTextField } from '@/utils/components'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { CustomFileUploader, CustomProfileElement } from '@/components'
import { User } from '@/interfaces'

type Network = {
	id: string
	name: string
}
type SupervisorFormProps = {
	form: UseFormReturn<TAddSupervisorDetailsSchema>
	showActions?: boolean
	onSubmit?: () => void
	onCancel?: () => void
	showUserInfo?: boolean
	selectedUser?: User | null
	assignNetworks?: boolean
	networks?: Network[]
	showIdentificationFields?: boolean
}

export const SupervisorForm = ({
	form,
	showActions = false,
	onSubmit,
	onCancel,
	showUserInfo = false,
	selectedUser,
	assignNetworks = false,
	networks,
	showIdentificationFields = true,
}: SupervisorFormProps) => {
	const {
		watch,
		setValue,
		clearErrors,
		register,
		handleSubmit,
		formState: { errors },
	} = form

	const [addIdentificationNumber, setAddIdentificationNumber] =
		useState<boolean>(false)
	const [showAssignNetworkField, setShowAssignNetworkField] =
		useState<boolean>(false)
	const isTrained = watch('trained')

	return (
		<Stack gap={2} p={3}>
			<Stack className='detail'>
				<Stack className='user-details' gap={3} alignItems='center'>
					<CustomProfileElement
						value={watch('profileImage')}
						setValue={(id, url) => {
							setValue('profileImage', { id, url })
						}}
						errorMessage={errors?.profileImage?.id?.message}
						clearErrors={() => clearErrors('profileImage')}
					/>

					{showUserInfo && selectedUser && (
						<Box className='user-info'>
							<Typography variant='h5'>{selectedUser?.name}</Typography>
							<Stack>
								<Box className='user-contact-info'>
									<MailOutline className='custom-icon' />
									<Typography>{selectedUser?.email}</Typography>
								</Box>
								<Box className='user-contact-info'>
									<Call className='custom-icon' />
									<Typography>{selectedUser?.number}</Typography>
								</Box>
							</Stack>
						</Box>
					)}
				</Stack>
			</Stack>

			<FormControl fullWidth>
				<CustomTextField
					schema={addSupervisorDetailsSchema}
					select
					{...register('trained')}
					label='Trained'
					id='select-type'
					disabled
					value={watch('trained') ? 'true' : 'false'}
					onChange={(event) => {
						setValue('trained', event.target.value === 'true')
						clearErrors('trained')
					}}
					error={!!errors.trained?.message}
					helperText={errors.trained?.message}>
					<MenuItem value='true'>Yes</MenuItem>
					<MenuItem value='false'>No</MenuItem>
				</CustomTextField>
			</FormControl>

			{isTrained && (
				<>
					<MultipleFileUploader
						data={(watch('trainingImages') ?? []).map((i) => ({
							...i,
							fileName: i?.path,
						}))}
						heading='Upload or Drag the Training Images'
						sx={{
							height: { xs: 100, md: 166 },
							width: '100%',
						}}
						imageHeight={100}
						setUploadData={(data) => {
							setValue('trainingImages', data)
							clearErrors('trainingImages')
						}}
					/>

					<FormHelperText error={Boolean(errors.trainingImages)}>
						{errors?.trainingImages?.message}
					</FormHelperText>
				</>
			)}

			{showIdentificationFields && (
				<Stack width='100%' flexDirection={'row-reverse'}>
					<Button onClick={() => setAddIdentificationNumber((p) => !p)}>
						{addIdentificationNumber ? <Remove /> : <Add />}
						<Typography sx={{ marginTop: 1 }}>
							Add Identification Number
						</Typography>
					</Button>
				</Stack>
			)}

			{addIdentificationNumber && (
				<FormControl fullWidth>
					<Stack>
						<CustomTextField
							label='Identification Number'
							value={watch('aadhaarNumber')}
							fullWidth
							onChange={(event) => {
								setValue('aadhaarNumber', event.target.value)
								clearErrors('aadhaarNumber')
							}}
							error={!!errors.aadhaarNumber?.message}
							helperText={errors.aadhaarNumber?.message}
						/>
					</Stack>
					<Stack marginTop={3}>
						<CustomFileUploader
							heading='Upload Identification No.'
							sx={{
								height: { xs: 100, md: 150 },
								width: '100%',
							}}
							mediaType='image'
							setUploadData={(data) => {
								setValue('aadhaarImage', data?.id)
								clearErrors('aadhaarImage')
							}}
						/>

						<FormHelperText error={Boolean(errors.aadhaarImage)}>
							{errors?.aadhaarImage?.message}
						</FormHelperText>
					</Stack>
				</FormControl>
			)}
			{assignNetworks && (
				<Stack width='100%' flexDirection={'row-reverse'}>
					<Button onClick={() => setShowAssignNetworkField((p) => !p)}>
						{showAssignNetworkField ? <Remove /> : <Add />}
						<Typography sx={{ marginTop: 1 }}>Assign Networks</Typography>
					</Button>
				</Stack>
			)}

			{showAssignNetworkField && (
				<FormControl fullWidth>
					<InputLabel>Assign Networks</InputLabel>
					<Select
						multiple
						id='networks'
						sx={{ height: 55 }}
						label='Assign Networks'
						variant='outlined'
						value={watch('networks')}
						{...register('networks')}>
						{networks?.map((item) => (
							<MenuItem key={item.id} value={item.id}>
								{item.name}
							</MenuItem>
						))}
					</Select>
				</FormControl>
			)}

			{showActions && (
				<Stack direction='row' gap={2} mt={2}>
					<Button variant='outlined' fullWidth onClick={onCancel}>
						Cancel
					</Button>
					<Button
						variant='contained'
						fullWidth
						onClick={handleSubmit(onSubmit || (() => {}))}>
						Save
					</Button>
				</Stack>
			)}
		</Stack>
	)
}
