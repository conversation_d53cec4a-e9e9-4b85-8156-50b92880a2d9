import {
	CustomCard,
	CustomChip,
	CustomPagination,
	CustomPaginationsDetails,
	ToggleQueryInput,
} from '@/components'
import {
	alpha,
	Box,
	CircularProgress,
	IconButton,
	Stack,
	Tooltip,
	Typography,
} from '@mui/material'

import BusinessIcon from '@mui/icons-material/Business'
import LanOutlinedIcon from '@mui/icons-material/LanOutlined'
import FmdGoodOutlinedIcon from '@mui/icons-material/FmdGoodOutlined'
import { MultipleAvatarWrapper } from '@/components/MultipleAvatarWrapper'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Block, CheckCircle } from '@mui/icons-material'
import EditIcon from '@/assets/icons/editIcon.svg'
import { Entity } from '@/interfaces'
import {
	entitiesRoles,
	entitiesRolesNames,
	StatusFilterOptions,
	userRoles,
} from '@/utils/constant'
import { useCallback, useMemo, useState } from 'react'
import { theme } from '@/lib/theme/theme'
import { CustomFilter } from '@/components/CustomFilter'
import { useAuthContext } from '@/contexts'
import { Confirmation } from '@/components/Confirmation/Confirmation'

type EntitiesListPropType = {
	isEntitiesLoading: boolean
	entitiesList: Entity[]
	totalEntitiesCount: number
	handleSuspendEntity: (id: string, isSuspend: boolean, tab: string) => void
	setShowEditEntities: (value: string) => void
}

const StatusTypeList = [
	{
		label: 'All',
		value: StatusFilterOptions.all,
	},
	{
		label: 'Active',
		value: StatusFilterOptions.Active,
	},
	{
		label: 'Suspended',
		value: StatusFilterOptions.Suspended,
	},
]

enum entitiesRolesForEdit {
	csink_manager = 'cSinkManager',
	artisan_pro = 'aps',
	csink_network = 'cSinkNetwork',
}

export default function EntitiesList({
	isEntitiesLoading,
	entitiesList,
	totalEntitiesCount,
	handleSuspendEntity,
	setShowEditEntities,
}: EntitiesListPropType) {
	const { userDetails } = useAuthContext()

	const EntitiesTypeList = useMemo(
		() => [
			...([userRoles.Admin].includes(userDetails?.accountType as userRoles)
				? [
						{
							label: 'Company',
							value: entitiesRoles.CsinkManager,
						},
				  ]
				: []),
			{
				label: 'Artisan Pro',
				value: entitiesRoles.ArtisanPro,
			},
			{
				label: 'CSink Network',
				value: entitiesRoles.cSinkNetwork,
			},
		],
		[userDetails?.accountType]
	)
	return (
		<>
			<Stack className='sticky-header' justifyContent='space-between'>
				<Stack
					paddingX={theme.spacing(2)}
					display={'flex'}
					flexDirection={'row'}
					gap={theme.spacing(1)}
					alignItems={'center'}>
					<BusinessIcon color='primary' />
					<Typography variant='body1' className='entities-heading'>
						{/* Networks */}
						Networks ({totalEntitiesCount})
					</Typography>
				</Stack>

				<Stack
					flexDirection={'row'}
					justifyContent={'flex-end'}
					alignItems={'center'}
					gap={theme.spacing(0.5)}>
					<ToggleQueryInput
						queryKey='entitySearch'
						className='search-textField'
						placeholder='Search'
						setPageOnSearch
					/>
					<CustomFilter
						queryKey='types'
						filtersToReset={['page', 'limit']}
						label='All Types'
						options={EntitiesTypeList}
						style={{ width: theme.spacing(15) }}
						showCheckbox={false}
						isCompact
					/>
					<CustomFilter
						queryKey='entityStatus'
						initialValue={[StatusFilterOptions.Active]}
						filtersToReset={['page', 'limit', 'entityId', 'entityStatus']}
						label='Status'
						options={StatusTypeList}
						style={{ width: theme.spacing(15) }}
						multiple={false}
						isCompact
					/>
					<CustomPaginationsDetails
						pageName='entityPage'
						limitName='entityLimit'
						rowCount={totalEntitiesCount}
						alignRowOrColumn='column'
					/>
				</Stack>
			</Stack>
			<Box className='entities-box'>
				{isEntitiesLoading ? (
					<Stack alignItems={'center'}>
						<Box
							component={CircularProgress}
							alignSelf='center'
							mt={theme.spacing(2)}
						/>
					</Stack>
				) : entitiesList.length === 0 ? (
					<Stack alignItems={'center'}>
						<Typography
							padding={theme.spacing(2)}
							maxWidth={theme.spacing(50)}
							textAlign={'center'}>
							No Network is created, click on Add button to create Csink Network
							or Artisan Pro
						</Typography>
					</Stack>
				) : (
					<>
						<Stack
							direction={'column'}
							padding={theme.spacing(1)}
							gap={theme.spacing(1)}>
							{entitiesList?.map((entitie) => (
								<EntitiesItem
									key={entitie.id}
									item={entitie}
									handleSuspendEntity={handleSuspendEntity}
									setShowEditEntities={setShowEditEntities}
								/>
							))}
						</Stack>
						<Stack
							flexDirection={'row'}
							justifyContent={'center'}
							padding={theme.spacing(1)}>
							<CustomPagination
								pageName='entityPage'
								limitName='entityLimit'
								rowCount={totalEntitiesCount}
							/>
						</Stack>
					</>
				)}
			</Box>
		</>
	)
}

const EntitiesItem = ({
	item,
	handleSuspendEntity,
	setShowEditEntities,
}: {
	item: Entity
	handleSuspendEntity: (id: string, isSuspend: boolean, tab: string) => void
	setShowEditEntities: (value: string) => void
}) => {
	const navigate = useNavigate()
	const { userDetails } = useAuthContext()
	const [selectedEntityIdParams] = useSearchParams()
	const entityId = selectedEntityIdParams.get('entityId')
	const editEntityId = selectedEntityIdParams.get('editEntityId')
	const images = item?.managers.map((manager) => ({
		id: manager?.profileImage?.id,
		url: manager?.profileImage?.url,
		path: manager?.profileImage?.path,
	}))

	const handleChangeEntitieSelection = useCallback(() => {
		if (entityId === item.id) {
			selectedEntityIdParams.delete('tab')
			selectedEntityIdParams.delete('entityId')
			selectedEntityIdParams.delete('selectedEntityType')
			selectedEntityIdParams.delete('editEntityId')
		} else {
			selectedEntityIdParams.set('entityId', item.id)
			selectedEntityIdParams.set('selectedEntityType', item.entityType)
			selectedEntityIdParams.delete('editEntityId')
		}

		navigate(`?${selectedEntityIdParams.toString()}`, { replace: true })
	}, [entityId, item.id, item.entityType, navigate, selectedEntityIdParams])

	const handleEditButtonClicked = useCallback(() => {
		if (editEntityId !== item.id) {
			selectedEntityIdParams.set('editEntityId', item.id)
		}
		setShowEditEntities(entitiesRolesForEdit[item.entityType])

		navigate(`?${selectedEntityIdParams.toString()}`, { replace: true })
	}, [
		item.id,
		item.entityType,
		setShowEditEntities,
		navigate,
		selectedEntityIdParams,
		editEntityId,
	])

	const [selectedEntitieTypeParams] = useSearchParams()
	const entitieId = selectedEntitieTypeParams.get('entityId')

	const canSuspendOrActivate = useCallback(
		(item: Entity) => {
			switch (userDetails?.accountType) {
				case userRoles.Admin:
					return true
				case userRoles.CsinkManager:
					return item?.entityType !== entitiesRoles.CsinkManager
				case userRoles.ArtisanPro:
				case userRoles.cSinkNetwork:
				case userRoles.Manager:
					return false
				default:
					return false
			}
		},
		[userDetails?.accountType]
	)

	const canEditEntity = useCallback(
		(item: Entity) => {
			switch (userDetails?.accountType) {
				case userRoles.Admin:
				case userRoles.CsinkManager:
					return !item?.suspended
				case userRoles.ArtisanPro:
					return (
						item?.entityType === entitiesRoles.ArtisanPro && !item?.suspended
					)
				case userRoles.cSinkNetwork:
					return (
						item?.entityType === entitiesRoles.cSinkNetwork && !item?.suspended
					)
				case userRoles.Manager:
					return (
						(item?.entityType === entitiesRoles.ArtisanPro ||
							item?.entityType === entitiesRoles.cSinkNetwork) &&
						!item?.suspended
					)
				default:
					return false
			}
		},
		[userDetails?.accountType]
	)

	const [confirmDailog, setConfirmDailog] = useState<{
		val: boolean
		type: string
	} | null>(null)
	const handleOpenMap = (
		e: React.MouseEvent,
		coordinate?: { x: number; y: number }
	) => {
		e.stopPropagation()
		if (coordinate) {
			const mapUrl = `https://www.google.com/maps?q=${coordinate.x},${coordinate.y}`
			window.open(mapUrl, '_blank', 'noopener,noreferrer')
		}
	}

	return (
		<>
			<CustomCard
				sx={{
					cursor: 'pointer',
					backgroundColor:
						entitieId === item.id
							? alpha(theme.palette.primary.main, 0.05)
							: 'transparent',
					border: `1px solid ${
						entitieId === item.id
							? theme.palette.primary.main
							: theme.palette.neutral['100']
					}`,
				}}
				onClick={handleChangeEntitieSelection}
				key={item.id}
				headerComponent={
					<Stack flexDirection={'row'} gap={theme.spacing(2)}>
						<LanOutlinedIcon color='primary' className='entity-icon' />
						<Stack width={'100%'} direction={'column'} gap={theme.spacing(0)}>
							<Stack
								display={'flex'}
								flexDirection={'row'}
								justifyContent={'space-between'}>
								<Stack
									paddingLeft={theme.spacing(0.5)}
									flexDirection='row'
									alignItems='center'
									gap={theme.spacing(2)}>
									<Typography
										variant='body1'
										fontWeight={theme.typography.caption.fontWeight}>
										{item.name}
									</Typography>
									<Stack flexDirection={'row'} gap={theme.spacing(1)}>
										<CustomChip
											label={entitiesRolesNames[item?.entityType]}
											appliedClass='transparent'
											isSmall
										/>
										{item.suspended ? (
											<CustomChip
												label='Suspended'
												appliedClass='rejected'
												isSmall
											/>
										) : (
											<CustomChip
												label='Active'
												appliedClass='approved'
												isSmall
											/>
										)}
									</Stack>
								</Stack>
								<Stack flexDirection={'row'} gap={theme.spacing(2)}>
									{canEditEntity(item) ? (
										<Tooltip key={item.id} title='Edit' placement='top'>
											<IconButton
												onClick={(e) => {
													e.stopPropagation()
													handleEditButtonClicked()
												}}
												size='small'
												sx={{ padding: 0, height: theme.spacing(2.5) }}>
												<Box
													component='img'
													src={EditIcon}
													sx={{
														filter: 'grayscale(100%) brightness(0.5)',
														display: 'block',
													}}
													width={theme.spacing(2.5)}
												/>
											</IconButton>
										</Tooltip>
									) : null}
									{canSuspendOrActivate(item) ? (
										item?.suspended ? (
											<Tooltip
												key={item.id}
												title='Mark Active'
												placement='top'>
												<IconButton
													aria-label='Mark active'
													onClick={(e) => {
														e.stopPropagation()
														setConfirmDailog({ val: true, type: 'activate' })
													}}
													size='small'
													sx={{
														color: theme.palette.custom.green[900],
														padding: 0,
														height: theme.spacing(2.5),
													}}>
													<CheckCircle sx={{ fontSize: theme.spacing(2.5) }} />
												</IconButton>
											</Tooltip>
										) : (
											<Tooltip
												key={item.id}
												title='Mark Suspend'
												placement='top'>
												<IconButton
													onClick={(e) => {
														e.stopPropagation()
														setConfirmDailog({ val: true, type: 'suspend' })
													}}
													size='small'
													color='primary'
													sx={{ padding: 0, height: theme.spacing(2.5) }}>
													<Block sx={{ fontSize: theme.spacing(2.5) }} />
												</IconButton>
											</Tooltip>
										)
									) : null}
								</Stack>
							</Stack>
							{item?.location && (
								<Stack
									flexDirection={'row'}
									alignItems='center'
									gap={theme.spacing(0.5)}>
									<FmdGoodOutlinedIcon sx={{ width: theme.spacing(2) }} />
									<Typography
										variant='caption'
										sx={{ cursor: 'pointer' }}
										onClick={(e) => handleOpenMap(e, item.coordinate)}>
										{item.location}
									</Typography>
								</Stack>
							)}

							<Box
								component={Stack}
								alignItems='start'
								gap={1}
								sx={{
									cursor: 'pointer',
								}}>
								<Tooltip
									title={
										<Stack direction='column'>
											{(item?.managers ?? [])?.map((item) => (
												<Typography key={item?.id} variant='caption'>
													{item?.name}
												</Typography>
											))}
										</Stack>
									}
									placement='bottom-start'
									arrow>
									<Box>
										<MultipleAvatarWrapper
											images={images}
											length={item?.managers?.length || 0}
											MaxAvatar={2}
										/>
									</Box>
								</Tooltip>
							</Box>
						</Stack>
					</Stack>
				}
			/>
			<Confirmation
				confirmationText={`Are you sure you want to ${
					confirmDailog?.type === 'suspend' ? 'suspend' : 'activate'
				} this Entity ?`}
				open={!!confirmDailog && confirmDailog?.val}
				handleClose={() => setConfirmDailog(null)}
				handleNoClick={() => setConfirmDailog(null)}
				handleYesClick={() => {
					confirmDailog?.type === 'suspend'
						? handleSuspendEntity(item.id, true, item.entityType)
						: handleSuspendEntity(item.id, false, item.entityType)
				}}
			/>
		</>
	)
}
