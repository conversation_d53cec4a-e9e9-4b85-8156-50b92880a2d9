import { Close } from '@mui/icons-material'
import {
	Button,
	Dialog,
	Divider,
	FormControl,
	FormHelperText,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Typography,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useEffect } from 'react'
import { styled } from '@mui/material/styles'
import { demoteCsinkManagerSchema, TDemoteCsinkManager } from './schema'
import { User } from '@/interfaces'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { AssignEntityListSideDrawer } from '@/components/AssignEntityListSideDrawer'
type Props = {
	selectedUser: User | null
	closeDrawer: () => void
	demoteUser: (user: { id: string; name: string; isArtisan: boolean }[]) => void
}

export const DemoteCsinkManagerDialog = ({
	selectedUser,
	demoteUser,
	closeDrawer,
}: Props) => {
	const form = useForm<TDemoteCsinkManager>({
		resolver: yupResolver(demoteCsinkManagerSchema),
		mode: 'all',
		defaultValues: {
			networks: [],
		},
	})
	const { data } = useQuery({
		queryKey: ['networks', selectedUser?.id],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				networks: { id: string; name: string ,isArtisan: boolean }[]
			}>(`/new/networks?csinkManagerId=${selectedUser?.csinkManagerId}`)
			setValue(
				'networks',
				data?.networks?.map((i) => i.id)
			)
			return data
		},

		enabled: !!selectedUser?.id,
	})

	const { setValue, getValues, watch, handleSubmit, register } = form

	console.log({ errors: form.formState?.errors, values: getValues() })

	// useEffect(() => {
	// 	if (!selectedUser) return
	// 	setValue(
	// 		'networks',
	// 		networks.map((i) => i.id)
	// 	)
	// }, [setValue, selectedUser, networks])

	const onSubmit = () => {
        const arr=watch('networks')?.map((i)=>{
            return data?.networks?.find((j) => j.id === i)
        })
		demoteUser(arr ?? [])
	}

	return (
        
		// <AssignEntityListSideDrawer
		// 	handleCloseDrawer={closeDrawer}
		// 	handleAdd={(ids) => demoteUser({ networks: ids })}
        //     subheading='Please Assign the networks.'
		// 	heading={'Demote Users'}
		// 	options={data?.networks ?? []}
		// 	loading={isLoading}
		// />
        <Dialog open={true} onClose={closeDrawer} fullWidth maxWidth='sm'>

		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h4'>Demote User</Typography>
				<IconButton onClick={closeDrawer}>
					<Close />
				</IconButton>
			</Stack>
			<Divider />
			<Stack className='container'>
				<FormControl fullWidth>
					<InputLabel>Assign Networks</InputLabel>
					<Select
						multiple
						id='networks'
						error={!!form.formState?.errors?.networks}
						sx={{ height: 55 }}
						label='Assign Networks'
						variant='outlined'
						value={watch('networks')}
						{...register('networks')}>
						{data?.networks?.map((item) => (
							<MenuItem key={item.id} value={item.id}>
								{item.name}
							</MenuItem>
						))}
					</Select>
					<FormHelperText error={!!form.formState?.errors?.networks}>
						{form.formState?.errors?.networks?.message}
					</FormHelperText>
				</FormControl>
				<Stack direction='row' gap={2} mt={2}>
					<Button variant='outlined' fullWidth onClick={closeDrawer}>
						Cancel
					</Button>
					<Button
						variant='contained'
						fullWidth
						onClick={handleSubmit(onSubmit || (() => {}))}>
						Save
					</Button>
				</Stack>
			</Stack>
		</StyleContainer>
        </Dialog>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2.5),
	},
	'.container': {
		padding: theme.spacing(2.5),
		gap: theme.spacing(4),
	},
}))
