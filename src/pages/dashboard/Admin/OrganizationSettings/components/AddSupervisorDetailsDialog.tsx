import { theme } from '@/lib/theme/theme'
import { Close } from '@mui/icons-material'

import {
	Box,
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	Stack,
	Typography,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import {
	addSupervisorDetailsSchema,
	TAddSupervisorDetailsSchema,
} from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { SupervisorForm } from './SupervisorForm'

export interface SupervisorDetailsData {
	profileImage?: { id: string; url?: string };
	trainingImages?: { id: string }[];
	aadhaarNumber?: string | null;
	aadhaarImage?: string | null;
}
interface AddSupervisorDetailsDialogProps {
	open: boolean
	onClose: () => void
	onSubmit: (supData: SupervisorDetailsData) => void
}
export const AddSupervisorDetailsDialog: React.FC<
	AddSupervisorDetailsDialogProps
> = ({ open, onClose, onSubmit }) => {
	const form = useForm<TAddSupervisorDetailsSchema>({
		resolver: yupResolver(addSupervisorDetailsSchema),
		mode: 'all',
		defaultValues: {
			trained: true,
			trainingImages: [],
			aadhaarNumber: null,
			aadhaarImage: null,
			profileImage: undefined,
		},
	})

	const { handleSubmit } = form
	const onSubmitForm = (values: TAddSupervisorDetailsSchema) => {
		onSubmit({
			profileImage: values.profileImage,
			trainingImages: values.trainingImages,
			aadhaarNumber: values.aadhaarNumber,
			aadhaarImage: values.aadhaarImage,
		})
	}
	return (
		<>
			<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
				<DialogTitle textAlign='center'>
					<Stack direction='row' alignItems='center' sx={{ width: '100%' }}>
						<Box flex={1} />
						<Typography
							align='center'
							fontWeight={theme.typography.caption.fontWeight}
							fontSize={theme.typography.h6.fontSize}
							color={theme.palette.primary.main}>
							Add Details
						</Typography>

						<Box flex={1} display='flex' justifyContent='flex-end'>
							<IconButton onClick={onClose}>
								<Close />
							</IconButton>
						</Box>
					</Stack>
					<Typography textAlign='center'>
						Please add following details for this user to <br /> make this user
						a supervisor.
					</Typography>
				</DialogTitle>
				<DialogContent>
					<SupervisorForm
						form={form}
					/>
				</DialogContent>

				<DialogActions
					sx={{
						px: theme.spacing(4),
						py: theme.spacing(2),
						justifyContent: 'center',
					}}>
					<Button
						variant='contained'
						sx={{ width: '30%' }}
						onClick={handleSubmit(onSubmitForm)}>
						Assign
					</Button>
				</DialogActions>
			</Dialog>
		</>
	)
}
