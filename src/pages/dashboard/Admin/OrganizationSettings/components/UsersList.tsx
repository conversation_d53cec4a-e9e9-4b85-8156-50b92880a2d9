import {
	CustomPagination,
	CustomPaginationsDetails,
	NoData,
} from '@/components'
import { User } from '@/interfaces'
import { Box, CircularProgress } from '@mui/material'
import { Stack } from '@mui/material'
import { userRoles } from '@/utils/constant'
import { CustomFilter } from '@/components/CustomFilter'
import { ILabelWithValue } from '@/types'
import { theme } from '@/lib/theme/theme'

import { UsersItem } from './UsersItem'
interface UsersListProps {
	isUsersLoading: boolean
	usersList: User[]
	totalUsersCount: number
	userFiltersOption: ILabelWithValue[]
	canEditOrDeleteUser: (value: userRoles, isEditButton: boolean) => boolean
	setShowEditUser: React.Dispatch<React.SetStateAction<User | null>>
	handleOpenDetails: (user: User) => void
	handleDeleteUser: (user: User | null) => void
}

export const UsersList = ({
	isUsersLoading,
	usersList,
	totalUsersCount,
	userFiltersOption,
	canEditOrDeleteUser,
	setShowEditUser,
	handleOpenDetails,
	handleDeleteUser,
}: UsersListProps) => {
	return (
		<>
			<Stack className='sticky-header' justifyContent='flex-end'>
				<Stack
					flexDirection={'row'}
					justifyContent={'flex-end'}
					alignItems={'center'}
					gap={theme.spacing(1)}>
					<CustomFilter
						queryKey='userRole'
						filtersToReset={['page', 'limit']}
						label='Users Type'
						multiple={false}
						options={userFiltersOption}
						style={{ width: 120 }}
						isCompact
					/>
					<CustomPaginationsDetails
						pageName='userPage'
						limitName='userLimit'
						rowCount={totalUsersCount}
						alignRowOrColumn='column'
					/>
				</Stack>
			</Stack>
			<Box className='user-box'>
				{!isUsersLoading && usersList.length === 0 && (
					<NoData
						noUsers={true}
						noDataMessage={
							<>
								No Users added, please add by clicking <br />
								on Add option from top
							</>
						}
					/>
				)}
				{isUsersLoading ? (
					<Stack alignItems={'center'}>
						<Box
							component={CircularProgress}
							alignSelf='center'
							mt={theme.spacing(2)}
						/>
					</Stack>
				) : (
					<>
						<Stack gap={theme.spacing(1)} padding={theme.spacing(1)}>
							{usersList.map((user) => (
								<UsersItem
									key={user.id}
									item={user}
									canEditOrDeleteUser={canEditOrDeleteUser}
									setShowEditUser={setShowEditUser}
									handleOpenDetails={handleOpenDetails}
									handleDeleteUser={handleDeleteUser}
								/>
							))}
						</Stack>
						<Stack flexDirection={'row'} justifyContent={'center'}>
							<CustomPagination
								pageName='userPage'
								limitName='userLimit'
								rowCount={totalUsersCount}
							/>
						</Stack>
					</>
				)}
			</Box>
		</>
	)
}
