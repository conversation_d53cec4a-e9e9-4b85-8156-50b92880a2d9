import { Close } from '@mui/icons-material'
import {
	<PERSON>ton,
	<PERSON><PERSON>,
	Divider,
	FormControl,
	FormHelperText,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Typography,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { styled } from '@mui/material/styles'
import {
	assignNetworkForPromoteDemoteSchema,
	TAssignNetworkForPromoteDemote,
} from './schema'
import { User } from '@/interfaces'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useEffect, useMemo } from 'react'
type Props = {
	selectedUser: User | null
	closeDrawer: () => void
	onSave: (networks: { id: string; name: string; isArtisan: boolean }[]) => void
	assignNetworksFor?: 'promote' | 'demote'
}

export const AssignNetworksForPromoteDemote = ({
	selectedUser,
	onSave,
	closeDrawer,
	assignNetworksFor = 'demote',
}: Props) => {
	const {
		setValue,
		watch,
		handleSubmit,
		register,
		formState: { errors },
	} = useForm<TAssignNetworkForPromoteDemote>({
		resolver: yupResolver(assignNetworkForPromoteDemoteSchema),
		mode: 'onChange',
		defaultValues: {
			networks: [],
		},
	})

	const { data } = useQuery({
		queryKey: ['networks', selectedUser?.csinkManagerId],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				networks: { id: string; name: string; isArtisan: boolean }[]
			}>(`/new/networks?csinkManagerId=${selectedUser?.csinkManagerId}`)

			// Pre-select all networks
			setValue('networks', data?.networks?.map((network) => network.id) || [])

			return data
		},
		enabled: !!selectedUser?.csinkManagerId && assignNetworksFor === 'demote',
	})

	const allNetworks = useMemo(() => {
		if (assignNetworksFor === 'demote') {
			return data?.networks
		}
		const networks = []
		if (selectedUser?.artisanPros && selectedUser?.artisanPros?.length > 0) {
			networks.push(
				...(selectedUser?.artisanPros
					? selectedUser.artisanPros.map((ap) => ({
							id: ap.id,
							name: ap.name,
							isArtisan: true,
					  }))
					: [])
			)
		}
		if (
			selectedUser?.csinkNetworks &&
			selectedUser?.csinkNetworks?.length > 0
		) {
			networks.push(
				...(selectedUser?.csinkNetworks
					? selectedUser.csinkNetworks.map((cn) => ({
							id: cn.id,
							name: cn.name,
							isArtisan: false,
					  }))
					: [])
			)
		}
		return networks
	}, [
		assignNetworksFor,
		data?.networks,
		selectedUser?.artisanPros,
		selectedUser?.csinkNetworks,
	])

	useEffect(() => {
		if (assignNetworksFor === 'promote') {
			setValue('networks', allNetworks?.map((network) => network.id) || [])
		}
	}, [assignNetworksFor])

	const selectedNetworks = watch('networks')

	const onSubmit = async () => {
		try {
			const networkMap = new Map(
				allNetworks?.map((network) => [network.id, network])
			)
			const selectedNetworkData =
				selectedNetworks
					?.map((networkId) => networkMap.get(networkId))
					.filter(
						(
							network
						): network is { id: string; name: string; isArtisan: boolean } =>
							network !== undefined
					) || []
			await onSave(selectedNetworkData)
			closeDrawer()
		} catch (error) {
			console.error('Error saving network assignments:', error)
			// Consider showing a user-friendly error message here
		}
	}

	return (
		<Dialog open={!!selectedUser} onClose={closeDrawer} fullWidth maxWidth='sm'>
			<StyleContainer>
				<Stack className='header'>
					<Typography variant='h4'>
						{assignNetworksFor === 'promote' ? 'Promote' : 'Demote'} User
					</Typography>
					<IconButton onClick={closeDrawer}>
						<Close />
					</IconButton>
				</Stack>
				<Divider />
				<Stack className='container'>
					<FormControl fullWidth>
						<InputLabel>Select Networks</InputLabel>
						<Select
							multiple
							id='networks'
							error={!!errors?.networks}
							sx={{ height: 55 }}
							label='Select Networks'
							variant='outlined'
							value={selectedNetworks || []}
							{...register('networks')}>
							{allNetworks?.map((network) => (
								<MenuItem key={network.id} value={network.id}>
									{network.name}
								</MenuItem>
							))}
						</Select>
						<FormHelperText error={!!errors?.networks}>
							{errors?.networks?.message}
						</FormHelperText>
					</FormControl>
					<Stack direction='row' gap={2} mt={2}>
						<Button variant='outlined' fullWidth onClick={closeDrawer}>
							Cancel
						</Button>
						<Button
							variant='contained'
							fullWidth
							onClick={handleSubmit(onSubmit)}>
							{assignNetworksFor === 'promote' ? 'Promote' : 'Demote'}
						</Button>
					</Stack>
				</Stack>
			</StyleContainer>
		</Dialog>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2.5),
	},
	'.container': {
		padding: theme.spacing(2.5),
		gap: theme.spacing(4),
	},
}))
