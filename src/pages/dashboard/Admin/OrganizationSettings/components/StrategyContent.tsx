import { entitiesRoles, EntityTabEnum } from '@/utils/constant'
import {
	Box,
	CircularProgress,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { Close } from '@mui/icons-material'
import {
	IArtisanProDetails,
	IFileData,
	IMedia,
	INetwork,
	TBiomassProcessingDetails,
	TMethaneCompensateStrategies,
} from '@/interfaces'
import { capitalizeFirstLetter } from '@/utils/helper'
import { TwoColumnLayout } from '@/components/TwoColumnLayout/TwoColumnLayout'
import { ActionInformationDrawer } from '@/components/ActionInformationDrawer/actionInformationDrawer'
import { EditBiomassPreProcess } from '@/components/EditNetwork/EditBiomassPreProcess'

import { NoData } from '@/components/NoData/NoData'
import { TrainingProofRenderer } from '@/components/TrainingProofRenderer/TrainingProofRenderer'
import { EditMethaneCompensate } from '@/components/EditNetwork'
import { useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import React, { useMemo } from 'react'
import { theme } from '@/lib/theme/theme'

interface StrategyContentProps {
	type: EntityTabEnum
	artisanProDetails?: IArtisanProDetails
	handleClose: () => void
	entityUserId: string | undefined
	editItem: boolean
	setEditItem: React.Dispatch<React.SetStateAction<boolean>>
	selectedMethaneCompensateId: string
	selectedBiomassType?: string
	addOrEdit: boolean
}

const TagLine = ({ label, value = '' }: { label: string; value?: string }) => (
	<Stack gap={1} flexDirection={'row'} height={'100%'} alignItems={'center'}>
		<Typography
			variant='subtitle2'
			fontSize={theme.typography.subtitle1.fontSize}>
			{label}:
		</Typography>
		<Typography
			variant='subtitle1'
			fontSize={theme.typography.subtitle1.fontSize}
			color={'text.secondary'}>
			{value || '-'}
		</Typography>
	</Stack>
)

const EditableItem = ({
	label,
	value,
}: {
	label: string
	value: string | undefined
}) => (
	<TwoColumnLayout
		left={<TagLine label={label} value={value} />}
		right={<></>}
		gridBreakpoints={[10, 2]}
	/>
)

const MediaSection = ({ documents }: { documents?: IFileData[] }) => (
	<Stack flexWrap='wrap' mb={1}>
		<TrainingProofRenderer
			viewMode='table'
			showDocumentName
			componentSize={60}
			ShowDeleteOption={false}
			media={(documents as IMedia[]) || []}
		/>
	</Stack>
)

const useSingleCSinkNetworkDetails = (
	cSinkNetworkId: string,
	enabled = true
) => {
	return useQuery({
		queryKey: ['cSinkNetworkDetails', cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios.get<INetwork>(
				`/cs-network/${cSinkNetworkId}`
			)
			return data
		},
		enabled,
	})
}

const useSingleArtisanProDetails = (artisanProId: string, enabled = true) => {
	return useQuery({
		queryKey: ['artisanProDetail', artisanProId],
		queryFn: async ({ queryKey }) => {
			const artisanProId = queryKey[1]
			const { data } = await authAxios.get<IArtisanProDetails>(
				`/artisian-pro/${artisanProId}`
			)
			return data
		},
		enabled,
	})
}

export const StrategyContent = ({
	type,
	handleClose,
	entityUserId,
	editItem,
	setEditItem,
	selectedMethaneCompensateId,
	selectedBiomassType,
	addOrEdit,
}: StrategyContentProps) => {
	const [searchParams] = useSearchParams()
	const selectedEntityType = searchParams.get('selectedEntityType')

	const isCsink = selectedEntityType === entitiesRoles.cSinkNetwork

	const { data: csinkNetworkData, isLoading: isCsinkLoading } =
		useSingleCSinkNetworkDetails(
			entityUserId as string,
			isCsink && !!entityUserId
		)

	const { data: artisanProData, isLoading: isArtisanLoading } =
		useSingleArtisanProDetails(
			entityUserId as string,
			!isCsink && !!entityUserId
		)

	const csinkNetworkDetails = useMemo(() => {
		if (!csinkNetworkData) return undefined
		return {
			...csinkNetworkData,
			methaneCompensateStrategies: selectedMethaneCompensateId
				? csinkNetworkData?.methaneCompensateStrategies?.filter(
						(strategy) => strategy.id === selectedMethaneCompensateId
				  ) ?? []
				: [],
		} as INetwork
	}, [csinkNetworkData, selectedMethaneCompensateId])

	const artisanProDetails = useMemo(() => {
		if (!artisanProData) return undefined
		return {
			...artisanProData,
			methaneCompensateStrategies: selectedMethaneCompensateId
				? artisanProData?.methaneCompensateStrategies?.filter(
						(strategy) => strategy.id === selectedMethaneCompensateId
				  ) ?? []
				: [],
		} as IArtisanProDetails
	}, [artisanProData, selectedMethaneCompensateId])

	const data =
		csinkNetworkDetails?.biomassPreprocessingDetails ??
		artisanProDetails?.biomassPreprocessingDetails

	const methaneData =
		csinkNetworkDetails?.methaneCompensateStrategies ??
		artisanProDetails?.methaneCompensateStrategies

	if ((isCsink && isCsinkLoading) || (!isCsink && isArtisanLoading)) {
		return (
			<Box
				component={CircularProgress}
				alignSelf='center'
				mt={theme.spacing(2)}
			/>
		)
	}

	return (
		<>
			{editItem && type === EntityTabEnum.biomassProcessingStrategy && (
				<ActionInformationDrawer
					open={editItem}
					onClose={() => {
						handleClose()
						setEditItem(false)
					}}
					anchor='right'
					component={
						<EditBiomassPreProcess
							isCsink={
								searchParams.get('selectedEntityType') ===
								entitiesRoles.cSinkNetwork
							}
							handleCloseDrawer={() => {
								handleClose()
								setEditItem(false)
							}}
							csinkNetworkDetails={csinkNetworkDetails}
							artisanProDetails={artisanProDetails}
						/>
					}
				/>
			)}
			{addOrEdit && type === EntityTabEnum.methaneCompensationStrategy && (
				<ActionInformationDrawer
					open={addOrEdit}
					onClose={handleClose}
					anchor='right'
					component={
						<EditMethaneCompensate
							editMode={editItem}
							isCsink={
								searchParams.get('selectedEntityType') ===
								entitiesRoles.cSinkNetwork
							}
							selectedMethaneCompensateId={selectedMethaneCompensateId}
							artisanProDetails={artisanProDetails}
							handleCloseDrawer={handleClose}
							csinkNetworkDetails={csinkNetworkDetails}
						/>
					}
				/>
			)}
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Typography variant='body2' fontSize={theme.spacing(2)}>
							{type === EntityTabEnum.biomassProcessingStrategy
								? 'Biomass Pre-Processing Strategy'
								: 'Methane Compensate Strategy'}
						</Typography>
						<IconButton onClick={handleClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='container' gap={2}>
					<DisplayContent
						selectedBiomassType={selectedBiomassType as string}
						data={data as TBiomassProcessingDetails}
						methaneData={methaneData as TMethaneCompensateStrategies[]}
						type={type as EntityTabEnum}
					/>
				</Stack>
			</StyleContainer>
		</>
	)
}
const DisplayContent = React.memo(
	({
		selectedBiomassType,
		data,
		methaneData,
		type,
	}: {
		selectedBiomassType: string
		data: TBiomassProcessingDetails
		methaneData: TMethaneCompensateStrategies[]
		type: EntityTabEnum
	}) => {
		const renderBiomassStrategy = (
			<Stack gap={1.5} spacing={1}>
				{selectedBiomassType && selectedBiomassType === 'drying' ? (
					<>
						<EditableItem
							label='Drying Type'
							value={capitalizeFirstLetter(data?.dryingType)
								.split('_')
								.join(' ')}
						/>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Description:
							</Typography>
							<Typography
								variant='subtitle1'
								color={'text.secondary'}
								fontSize={theme.typography.subtitle1.fontSize}>
								{capitalizeFirstLetter(data?.dryingStrategy)
									.split('_')
									.join(' ') || '-'}
							</Typography>
						</Stack>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Documents:{' '}
							</Typography>
							<MediaSection documents={data?.dryingDocuments ?? []} />
						</Stack>
					</>
				) : (
					<>
						<EditableItem
							label='Shredding Type'
							value={capitalizeFirstLetter(data?.shreddingType)
								.split('_')
								.join(' ')}
						/>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Description:
							</Typography>
							<Typography
								variant='subtitle1'
								fontSize={theme.typography.subtitle1.fontSize}
								color={'text.secondary'}>
								{capitalizeFirstLetter(data?.shreddingStrategy)
									.split('_')
									.join(' ') || '-'}
							</Typography>
						</Stack>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Documents:{' '}
							</Typography>
							<MediaSection documents={data?.shreddingDocuments ?? []} />
						</Stack>
					</>
				)}
			</Stack>
		)

		const renderMethaneStrategy = methaneData?.length ? (
			<Stack gap={4}>
				{methaneData?.map((methaneDetails, idx) => (
					<Stack gap={1.5} spacing={1} key={idx}>
						<Stack direction={'row'} alignItems='center' marginBottom={-1.5}>
							<TagLine
								label={'Methane Type'}
								value={
									capitalizeFirstLetter(methaneDetails?.methaneCompensateType)
										.split('_')
										.join(' ') +
										(methaneDetails?.compensateType
											? ' ( ' + methaneDetails?.compensateType + ' )'
											: ''
										)
											.toUpperCase()
											.split('_')
											.join(' ') || '-'
								}
							/>
						</Stack>
						<TagLine
							label={'Biomass Type'}
							value={
								capitalizeFirstLetter(methaneDetails?.biomassName)
									.split('_')
									.join(' ') || '-'
							}
						/>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Description:
							</Typography>
							<Typography
								variant='subtitle1'
								fontSize={theme.typography.subtitle1.fontSize}
								color={'text.secondary'}>
								{capitalizeFirstLetter(methaneDetails?.description)
									.split('_')
									.join(' ') || '-'}
							</Typography>
						</Stack>
						<Stack gap={theme.spacing(0.5)}>
							<Typography
								variant='subtitle2'
								fontSize={theme.typography.subtitle1.fontSize}>
								Documents:
							</Typography>
							<MediaSection documents={methaneDetails?.documents || []} />
						</Stack>
					</Stack>
				))}
			</Stack>
		) : (
			<NoData size='small' />
		)

		switch (type) {
			case EntityTabEnum.biomassProcessingStrategy:
				return renderBiomassStrategy
			case EntityTabEnum.methaneCompensationStrategy:
				return renderMethaneStrategy
			default:
				return <></>
		}
	}
)

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
	},
}))
