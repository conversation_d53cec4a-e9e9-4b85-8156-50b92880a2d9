import { ActionInformationDrawer, TrainingProofRenderer } from '@/components'
import { SelectApplicationTypeForLogOut } from '@/components/SelectApplicationTypeForLogOut'
import CertificateDialog from '@/components/ViewUserDetails/CertificateDialog'
import { authAxios, useAuthContext } from '@/contexts'
import {
	IArtisanPro,
	ICSinkNetwork,
	IFarmForUser,
	INetworksResponse,
	User,
} from '@/interfaces'
import {
	AssignNetworkDetails,
	userRoles,
	userRolesNameOrganizationSettings,
} from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import {
	Call,
	Close,
	Delete,
	Edit,
	FmdGoodOutlined,
	InsertDriveFile,
	Logout,
	MailOutline,
	NorthEast,
	Replay,
	SouthWest,
} from '@mui/icons-material'
import {
	Avatar,
	Box,
	Button,
	Chip,
	Grid,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import GenerateCertificateDrawer from '../../Settings/components/GenerateCertificateDrawer'
import { AddTrainingImages } from './AddTrainingImages'
import { useSearchParams } from 'react-router-dom'
import { theme } from '@/lib/theme/theme'
import { TAddSupervisorDetailsSchema } from './schema'
import { AssignNetworksDialog } from './AssignNetworksDialog'
import { Confirmation } from '@/components/Confirmation'
import { AssignNetworksForPromoteDemote } from './AssignNetworksForPromoteDemote'

type UserDetailViewProps = {
	closeDrawer: () => void
	selectedUser: User | null
	setShowEditUser: Dispatch<SetStateAction<User | null>>
	setShowConfirmationDialog: (show: boolean) => void
	onAssignComplete?: (details: AssignNetworkDetails) => void
}
type CompanyInfo = { name?: string; typeName?: string }
enum EentityTypeName {
	company = 'Company',
	cSinkNetwork = 'CSinK Network',
	artisanPro = 'Artisan Pro',
}
type IDemoteCsinkManagerPayload = {
	id: string
	name: string
	isArtisan: boolean
}[]

type ButtonVariant = 'text' | 'outlined' | 'contained'

const resetCountFn = async (userId: string) => {
	if (!userId) throw new Error('No user selected')
	const { data } = await authAxios.patch(
		`/user/${userId}/pending-uploads-count`
	)
	return data
}

const getEntityId = (user: User) => {
	switch (user.accountType) {
		case userRoles.cSinkNetwork:
		case userRoles.ArtisanPro:
		case userRoles.Manager:
			return user.csinkManagerId
		case userRoles.artisanProOperator:
			return user.artisanProId
		case userRoles.csinkNetworkOperator:
			return user.csinkNetworkId
		default:
			return undefined
	}
}

const getAccountTypeToPromote = (user: User) => {
	switch (user.accountType) {
		case userRoles.cSinkNetwork:
		case userRoles.ArtisanPro:
		case userRoles.Manager:
			return userRoles.CsinkManager
		case userRoles.artisanProOperator:
		case userRoles.csinkNetworkOperator:
			return userRoles.Manager
		case userRoles.Supervisor:
			return userRoles.Manager
		default:
			return undefined
	}
}

const promoteUserFn = async (
	selectedUser: User | null,
	values?: IDemoteCsinkManagerPayload
) => {
	if (!selectedUser) throw new Error('No user selected')

	const payload = {
		userId: selectedUser?.id,
		accountType: getAccountTypeToPromote(selectedUser),
		entityId: getEntityId(selectedUser),
		artisanProIds: values
			? getSelectedArtisanProIds(
					selectedUser,
					values as TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
			  )
			: undefined,
		csinkNetworkIds: values
			? getSelectedCsinkNetworkIds(
					selectedUser,
					values as TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
			  )	
			: undefined,
	}

	const apiUrl = `/user/${selectedUser?.id}/promote`
	const { data } = await authAxios.put(apiUrl, payload)
	return data
}

const getAccountTypeToDemote = (user: User) => {
	switch (user.accountType) {
		case userRoles.CsinkManager:
			return userRoles.Manager
		case userRoles.cSinkNetwork:
		case userRoles.ArtisanPro:
		case userRoles.Manager:
			return userRoles.Supervisor

		default:
			return undefined
	}
}
const getSelectedArtisanProIds = (
	user: User,
	values: TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
) => {
	if (Array.isArray(values)) {
		return values?.filter((item) => item.isArtisan).map((item) => item.id)
	}
	const networks =
		typeof values === 'object' && 'networks' in values ? values?.networks : []
	return user.artisanPros
		?.filter((ap: IArtisanPro) => networks?.includes(ap.id))
		?.map?.((ap: IArtisanPro) => ap.id)
}

const getSelectedCsinkNetworkIds = (
	user: User,
	values: TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
) => {
	if (Array.isArray(values)) {
		return values?.filter((item) => !item.isArtisan).map((item) => item.id)
	}
	const networks =
		typeof values === 'object' && 'networks' in values ? values?.networks : []

	return user.csinkNetworks
		?.filter((cn: ICSinkNetwork) => networks?.includes(cn.id))
		?.map?.((cn: ICSinkNetwork) => cn.id)
}

const demoteNetworkAdminToOperatorFn = async ({
	values,
	selectedUser,
}: {
	values?: TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
	selectedUser: User | null
}) => {
	if (!selectedUser) throw new Error('No user selected')

	const commonPayload = {
		userId: selectedUser?.id,
		accountType: getAccountTypeToDemote(selectedUser),
		entityId: getEntityId(selectedUser),
		artisanProIds: getSelectedArtisanProIds(
			selectedUser,
			values as TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
		),
		csinkNetworkIds: getSelectedCsinkNetworkIds(
			selectedUser,
			values as TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
		),
	}
	const payload = {
		...commonPayload,
		profileImageId:
			values && 'profileImage' in values
				? values.profileImage?.id || null
				: null,
		aadhaarNumber:
			values &&
			'aadhaarNumber' in values &&
			typeof values?.aadhaarNumber === 'string'
				? values?.aadhaarNumber === ''
					? null
					: values?.aadhaarNumber
				: null,
		aadhaarImageId:
			values && 'aadhaarImage' in values
				? values.aadhaarImage === ''
					? null
					: values.aadhaarImage
				: null,
		trainingImageIds:
			values && 'trainingImages' in values && values?.trainingImages?.length
				? values.trainingImages.map((trainingId) => trainingId?.id)
				: undefined,
	}

	const apiUrl = `/user/${selectedUser?.id}/demote`

	const { data } = await authAxios.put(
		apiUrl,
		selectedUser?.accountType === userRoles.CsinkManager
			? commonPayload
			: payload
	)
	return data
}

const addTrainingImagesFn = async ({
	selectedUser,
	values,
}: {
	selectedUser: User | null
	values: TAddSupervisorDetailsSchema
}) => {
	if (!selectedUser) throw new Error('No user selected')
	const { trainingImages, profileImage } = values

	const payload = {
		name: selectedUser?.name,
		email: selectedUser?.email,
		countryCode: selectedUser?.countryCode ?? null,
		phoneNumber: selectedUser?.number || null,
		trainingImageIds: trainingImages?.map((item) => item.id) ?? [],
		profileImageId: profileImage?.id ?? null,
	}

	const { data } = await authAxios.put(`/user/${selectedUser?.id}`, payload)
	return data
}
export const UserDetailView = ({
	closeDrawer,
	selectedUser,
	setShowEditUser,
	setShowConfirmationDialog,
	onAssignComplete,
}: UserDetailViewProps) => {
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const [showSelectApplicationTypeLogOut, setShowSelectApplicationTypeLogOut] =
		useState<boolean>(false)
	const [showCertificateDrawer, setShowCertificateDrawer] =
		useState<boolean>(false)
	const [showAddTrainingImagesDrawer, setShowAddTrainingImagesDrawer] =
		useState<boolean>(false)
	const [showTrainingImagesDrawer, setShowTrainingImagesDrawer] =
		useState<boolean>(false)
	const [showAssignNetworkDialog, setShowAssignNetworkDialog] =
		useState<boolean>(false)
	const farmDetails = useMemo(() => {
		return (
			selectedUser?.accountType === 'farmer' ||
			selectedUser?.accountType === 'csink_operator_farmer'
		)
	}, [selectedUser?.accountType])
	const [selectedEntitieTypeParams] = useSearchParams()

	const [showPromoteConfirmation, setShowPromoteConfirmation] =
		useState<boolean>(false)
	const [
		showPromoteWithAssignNetworkDialog,
		setShowPromoteWithAssignNetworkDialog,
	] = useState<boolean>(false)
	const [showDemoteCsinkManagerDialog, setShowDemoteCsinkManagerDialog] =
		useState<boolean>(false)
	const entityId = selectedEntitieTypeParams.get('entityId') || null
	const search = selectedEntitieTypeParams.get('userSearch') || ''

	const resetCountMutation = useMutation({
		mutationKey: ['resetCount'],
		mutationFn: () => resetCountFn(selectedUser?.id ?? ''),
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'Counts decreased successfully')
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
			closeDrawer()
		},
	})

	const promoteMutation = useMutation({
		mutationKey: ['promoteToCsinkNetworkManager'],
		mutationFn: (values?: IDemoteCsinkManagerPayload) =>
			promoteUserFn(selectedUser, values),
		onSuccess: (data) => {
			toast(data?.message)
			closeDrawer()
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			} else {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			}
			setShowPromoteConfirmation(false)
			setShowPromoteWithAssignNetworkDialog(false)
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const demoteNetworkAdminToOperator = useMutation({
		mutationKey: ['demoteNetworkAdminToOperator'],
		mutationFn: (
			values?: TAddSupervisorDetailsSchema | IDemoteCsinkManagerPayload
		) =>
			demoteNetworkAdminToOperatorFn({
				selectedUser,
				values,
			}),
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: (response) => {
			toast(response?.message)
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			} else {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			}
			setShowTrainingImagesDrawer(false)
			closeDrawer()
		},
	})

	const addTrainingImagesMutation = useMutation({
		mutationKey: ['editSupervisorMutation'],
		mutationFn: async (values: TAddSupervisorDetailsSchema) =>
			addTrainingImagesFn({
				selectedUser,
				values,
			}),
		onSuccess: (response) => {
			toast(response?.message)
			if (entityId)
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			else
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			setShowAddTrainingImagesDrawer(false)
			setShowCertificateDrawer(true)
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleResetCount = () => {
		resetCountMutation.mutate()
	}

	const handlePromote = () => {
		if (selectedUser?.accountType === userRoles.Supervisor) {
			if (selectedUser?.email) setShowPromoteWithAssignNetworkDialog(true)
			else {
				toast('Provide Email of this user')
				setShowEditUser(selectedUser)
			}
		} else {
			setShowPromoteConfirmation(true)
		}
	}

	const handleGenerateCertificate = () => {
		if (selectedUser?.trainingImageUrls?.length) {
			setShowCertificateDrawer(true)
		} else {
			toast('Provide Training images.')
			setShowAddTrainingImagesDrawer(true)
		}
	}
	const handleEdit = () => {
		setShowEditUser(selectedUser)
		closeDrawer()
	}
	const getFarmsDetails = useQuery({
		queryKey: ['farmList', selectedUser?.id],
		queryFn: async () =>
			authAxios.get<IFarmForUser[]>(`/new/farmers/${selectedUser?.id}/farms`),
		enabled: farmDetails,
	})
	const unassignedNetworks = useQuery({
		queryKey: ['unassignedNetworks', selectedUser?.id, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				search,
			})
			return authAxios.get<INetworksResponse>(
				`user/${
					selectedUser?.id
				}/unassigned-networks?${queryParams?.toString()}`
			)
		},
		enabled: showAssignNetworkDialog,
	})
	const showPromoteOption = useMemo(() => {
		switch (userDetails?.accountType) {
			case userRoles.Admin:
				return [
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles)
			case userRoles.CsinkManager:
				return [
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles)
			case userRoles.Manager:
			case userRoles.ArtisanPro:
			case userRoles.cSinkNetwork:
				return [
					userRoles.artisanProOperator,
					userRoles.Supervisor,
					userRoles.csinkNetworkOperator,
				].includes(selectedUser?.accountType as userRoles)
			default:
				return false
		}
	}, [selectedUser?.accountType, userDetails?.accountType])

	const showDemoteOption = useMemo(() => {
		switch (userDetails?.accountType) {
			case userRoles.Admin:
				return [
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
				].includes(selectedUser?.accountType as userRoles)
			case userRoles.CsinkManager:
				return [
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
				].includes(selectedUser?.accountType as userRoles)
			default:
				return false
		}
	}, [selectedUser?.accountType, userDetails?.accountType])

	const userActions = [
		{
			label: 'Edit User',
			icon: <Edit />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: handleEdit,
			show:
				([
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.csink_operator_farmer,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles) &&
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					)) ||
				[
					userRoles.csinkNetworkOperator,
					userRoles.artisanProOperator,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles) ||
				(selectedUser?.accountType === userRoles.CsinkManager &&
					selectedUser.id !== userDetails?.id),
		},
		{
			label: 'Delete User',
			icon: <Delete />,
			className: 'button-red',
			variant: 'outlined',
			onClick: () => setShowConfirmationDialog(true),
			show:
				([
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles) &&
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					)) ||
				(selectedUser?.accountType === userRoles.CsinkManager &&
					selectedUser.id !== userDetails?.id),
		},
		{
			label: 'Demote',
			icon: <SouthWest />,
			className: 'button-red',
			variant: 'outlined',
			onClick: () => {
				if (selectedUser?.accountType === userRoles.CsinkManager) {
					setShowDemoteCsinkManagerDialog(true)
				} else {
					setShowTrainingImagesDrawer(true)
				}
			},
			show: showDemoteOption,
		},
		{
			label: 'Promote',
			icon: <NorthEast />,
			className: 'button-green',
			variant: 'outlined',
			onClick: handlePromote,
			show: showPromoteOption,
		},
		{
			label: `Reset Count (${selectedUser?.pendingUploadsCount})`,
			icon: <Replay />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: handleResetCount,
			show:
				[
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin].includes(userDetails?.accountType as userRoles),
		},
		{
			label: 'Generate Cert',
			icon: <InsertDriveFile />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: handleGenerateCertificate,
			show:
				([
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					)) ||
				[
					userRoles.csinkNetworkOperator,
					userRoles.artisanProOperator,
					userRoles.Supervisor,
				].includes(selectedUser?.accountType as userRoles),
		},
		{
			label: 'Logout from App',
			icon: <Logout />,
			className: 'button-red-filled',
			variant: 'contained',
			onClick: () => setShowSelectApplicationTypeLogOut(true),
			show:
				[
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.Supervisor,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin].includes(userDetails?.accountType as userRoles),
		},
	]

	const showTraningImages = useMemo(() => {
		return Array.isArray(selectedUser?.trainingImageUrls)
	}, [selectedUser?.trainingImageUrls])

	const showIdentificationProof = useMemo(() => {
		return !!selectedUser?.aadhaarImageUrl
	}, [selectedUser?.aadhaarImageUrl])

	const showCertificates = useMemo(() => {
		return !!selectedUser?.certificateDetails
	}, [selectedUser?.certificateDetails])

	const isOnlyEmail = useMemo(() => {
		return [
			userRoles.ceres_auditor,
			userRoles.Admin,
			userRoles.compliance_manager,
			userRoles.CirconomyEmployee,
		].includes(selectedUser?.accountType as userRoles)
	}, [selectedUser?.accountType])

	const showIdentificationNumber = useMemo(() => {
		return [
			userRoles.csinkNetworkOperator,
			userRoles.artisanProOperator,
			userRoles.Supervisor,
		].includes(selectedUser?.accountType as userRoles)
	}, [selectedUser?.accountType])

	const companyInfo: CompanyInfo[] = useMemo(() => {
		const accountType = selectedUser?.accountType
		switch (accountType) {
			case userRoles.cSinkNetwork:
			case userRoles.csinkNetworkOperator:
				return [
					...(selectedUser?.csinkNetworks?.map((cn: IArtisanPro) => ({
						name: cn?.name,
						typeName: EentityTypeName.cSinkNetwork,
					})) || []),
				]

			case userRoles.ArtisanPro:
			case userRoles.artisanProOperator:
				return [
					...(selectedUser?.artisanPros?.map((ap: IArtisanPro) => ({
						name: ap?.name,
						typeName: EentityTypeName.artisanPro,
					})) || []),
				]
			case userRoles.Manager:
			case userRoles.Supervisor:
				return [
					...(selectedUser?.artisanPros?.map((ap: IArtisanPro) => ({
						name: ap?.name,
						typeName: EentityTypeName.artisanPro,
					})) || []),
					...(selectedUser?.csinkNetworks?.map((cn: ICSinkNetwork) => ({
						name: cn?.name,
						typeName: EentityTypeName.cSinkNetwork,
					})) || []),
				]
			case userRoles.farmer:
				return [{ name: selectedUser?.artisanProName, typeName: '' }]

			case userRoles.csink_operator_farmer:
				return [{ name: selectedUser?.csinkNetworkName, typeName: '' }]

			default:
				return []
		}
	}, [selectedUser])
	const networkUsers = useMemo(() => {
		return [
			userRoles.cSinkNetwork,
			userRoles.csinkNetworkOperator,
			userRoles.ArtisanPro,
			userRoles.artisanProOperator,
			userRoles.Manager,
			userRoles.Supervisor,
			userRoles.farmer,
			userRoles.csink_operator_farmer,
		].includes(selectedUser?.accountType as userRoles)
	}, [selectedUser?.accountType])
	const handleOpenMap = (
		e: React.MouseEvent,
		coordinateX: number,
		coordinateY: number
	) => {
		e.stopPropagation()
		if (coordinateX && coordinateY) {
			const mapUrl = `https://www.google.com/maps?q=${coordinateX},${coordinateY}`
			window.open(mapUrl, '_blank', 'noopener,noreferrer')
		}
	}

	const networksOptions = useMemo(() => {
		const networks = []
		if (selectedUser?.csinkNetworks && selectedUser.csinkNetworks.length > 0) {
			networks.push(...selectedUser.csinkNetworks.filter(Boolean))
		}

		if (selectedUser?.artisanPros && selectedUser.artisanPros?.length > 0) {
			networks.push(...selectedUser.artisanPros.filter(Boolean))
		}
		return networks
	}, [selectedUser?.artisanPros, selectedUser?.csinkNetworks])

	const handleDemoteUserWithSelectNetworks = useCallback(
		(values?: TAddSupervisorDetailsSchema) => {
			demoteNetworkAdminToOperator.mutate(values)
		},
		[demoteNetworkAdminToOperator]
	)

	const handleAddTrainingImages = useCallback(
		(values: TAddSupervisorDetailsSchema) => {
			addTrainingImagesMutation.mutate(values)
		},
		[addTrainingImagesMutation]
	)

	return (
		<>
			<ActionInformationDrawer
				open={showTrainingImagesDrawer}
				onClose={() => setShowTrainingImagesDrawer(false)}
				anchor='right'
				component={
					<AddTrainingImages
						setShowTrainingImagesDrawer={setShowTrainingImagesDrawer}
						selectedUser={selectedUser}
						closeDrawer={closeDrawer}
						networks={networksOptions}
						demoteUser={(values) => handleDemoteUserWithSelectNetworks(values)}
					/>
				}
			/>
			<ActionInformationDrawer
				open={showAddTrainingImagesDrawer}
				onClose={() => setShowAddTrainingImagesDrawer(false)}
				anchor='right'
				component={
					<AddTrainingImages
						setShowTrainingImagesDrawer={setShowAddTrainingImagesDrawer}
						selectedUser={selectedUser}
						closeDrawer={closeDrawer}
						showAssignNetworks={false}
						showUserInfo={false}
						showIdentificationFields={false}
						demoteUser={(values) => handleAddTrainingImages(values)} // callbackFunction
					/>
				}
			/>

			<ActionInformationDrawer
				open={showCertificateDrawer}
				onClose={() => setShowCertificateDrawer(false)}
				anchor='right'
				component={
					selectedUser ? (
						<GenerateCertificateDrawer
							onClose={() => {
								setShowCertificateDrawer(false)
								closeDrawer()
							}}
							userData={selectedUser}
						/>
					) : null
				}
			/>

			<SelectApplicationTypeForLogOut
				userId={selectedUser?.id || ''}
				open={showSelectApplicationTypeLogOut}
				onClose={() => setShowSelectApplicationTypeLogOut(false)}
				cb={() => {
					queryClient.invalidateQueries({ queryKey: ['users'] })
					closeDrawer()
				}}
			/>

			<StyleContainer>
				<Stack className='header'>
					<IconButton onClick={closeDrawer}>
						<Close />
					</IconButton>
				</Stack>

				<Stack className='detail'>
					<Stack className='user-details'>
						<Avatar
							alt={selectedUser?.name || 'User Avatar'}
							className='user-avatar'
							src={selectedUser?.profileImageUrl?.url || ''}
						/>

						<Stack>
							<Stack className='name-title' gap={theme.spacing(1)}>
								<Typography variant='h5'>{selectedUser?.name}</Typography>
								<Chip
									label={
										userRolesNameOrganizationSettings[
											selectedUser?.accountType as keyof typeof userRolesNameOrganizationSettings
										]
									}
									size='small'
									sx={{ alignSelf: 'flex-start' }}
								/>
							</Stack>
							<Stack spacing={0.5}>
								{selectedUser?.email && (
									<Stack className='user-contact-info'>
										<MailOutline color='disabled' className='custom-icon' />
										<Typography
											variant='body2'
											className='custom-contact-typography'>
											{selectedUser?.email}
										</Typography>
									</Stack>
								)}
								{selectedUser?.number && (
									<Stack className='user-contact-info'>
										<Call color='disabled' className='custom-icon' />
										<Typography
											variant='body2'
											className='custom-contact-typography'>
											{`${
												selectedUser?.countryCode
													? `(${selectedUser.countryCode}) `
													: ''
											}${selectedUser?.number ?? ''}`}
										</Typography>
									</Stack>
								)}
							</Stack>
						</Stack>
					</Stack>
					<Stack paddingTop={2}>
						{!isOnlyEmail && (
							<Typography
								fontSize={theme.spacing(1.5)}
								sx={{ fontWeight: 600 }}>
								Company Name:
							</Typography>
						)}
						<Typography sx={{ fontSize: theme.spacing(1.75) }}>
							{selectedUser?.csinkManagerName}
						</Typography>
					</Stack>
					<Stack spacing={0.5} mt={1.5}>
						{networkUsers && (
							<Stack direction='row' alignItems='center' gap={theme.spacing(3)}>
								<Typography
									fontSize={theme.spacing(1.5)}
									sx={{ fontWeight: 600 }}>
									Assigned Networks
								</Typography>
								{!farmDetails && (
									<Button
										variant='contained'
										className='assign-network-button'
										onClick={() => {
											setShowAssignNetworkDialog(true)
										}}>
										Assign Networks
									</Button>
								)}
							</Stack>
						)}
						{selectedUser?.accountType === userRoles.role_awaiting && (
							<Stack>
								<Typography
									fontSize={theme.spacing(1.5)}
									sx={{ fontWeight: 600 }}>
									Assigned Networks
								</Typography>
								<Stack alignItems='center' spacing={2}>
									<Button
										variant='contained'
										className='assign-network-button'
										onClick={() => {
											setShowAssignNetworkDialog(true)
										}}>
										Assign Networks
									</Button>
									<Typography
										textAlign='center'
										sx={{ fontSize: theme.spacing(1.6) }}>
										There is no role/network assigned to this user.
									</Typography>
								</Stack>
							</Stack>
						)}
						{companyInfo.map((info, i) => (
							<Typography
								key={i}
								sx={{
									fontSize: theme.spacing(1.75),
									paddingLeft: theme.spacing(0.75),
								}}>
								{info.name}
								{info.typeName ? ` (${info.typeName})` : ''}
							</Typography>
						))}
					</Stack>
					{networkUsers && (
						<Box
							sx={{
								borderBottom: `2px solid ${theme.palette.divider}`,
								paddingTop: '16px',
							}}></Box>
					)}
					{showIdentificationNumber && (
						<Stack marginTop={2}>
							<Typography variant='body1' sx={{ fontWeight: 500 }}>
								Identification No. : {selectedUser?.aadhaarNumber}
							</Typography>
						</Stack>
					)}

					{showTraningImages && (
						<Stack marginTop={2.5}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Training Images :
							</Typography>
							<TrainingProofRenderer
								media={(selectedUser?.trainingImageUrls ?? [])?.map((i) => ({
									...i,
									fileName: i?.path || i?.fileName,
									path: i?.path ? i.path : '',
								}))}
								viewMode='table'
								hideTitle
								showInRow
								componentSize={40}
								ShowDeleteOption={false}
							/>
						</Stack>
					)}

					{showIdentificationProof && (
						<Stack marginTop={3}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Identification Proof :
							</Typography>
							<TrainingProofRenderer
								media={
									selectedUser?.aadhaarImageUrl
										? [
												{
													...selectedUser.aadhaarImageUrl,
													fileName: selectedUser.aadhaarImageUrl.path,
													path: selectedUser.aadhaarImageUrl.path || '',
												},
										  ]
										: []
								}
								viewMode='table'
								hideTitle
								showInRow
								componentSize={40}
								ShowDeleteOption={false}
							/>
						</Stack>
					)}

					{showCertificates && (
						<Stack marginTop={3}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Certificates :
							</Typography>
							<CertificateDialog
								certificateData={selectedUser?.certificateDetails || {}}
							/>
						</Stack>
					)}
					{farmDetails && (
						<Stack>
							<Typography
								variant='body2'
								fontWeight='600'
								marginTop={theme.spacing(2)}>
								Farm details:
							</Typography>
							<Stack>
								{(getFarmsDetails?.data?.data ?? [])?.map((farm) => (
									<Stack
										flexDirection={'row'}
										alignItems='center'
										gap={theme.spacing(0.5)}>
										<FmdGoodOutlined
											color='error'
											sx={{ width: theme.spacing(2) }}
										/>
										<Typography
											className='font_size_14 first_letter_capitalize'
											sx={{
												cursor: 'pointer',
												'&:hover': {
													textDecoration: 'underline',
												},
											}}
											onClick={(e) =>
												handleOpenMap(
													e,
													farm?.farmLocation?.x,
													farm?.farmLocation?.y
												)
											}>
											{farm?.landmark} ({farm?.fieldSize} {farm?.fieldSizeUnit}){' '}
											{farm?.farmCrops?.length > 0 &&
												` - ${farm?.farmCrops
													?.map((crop) => crop.cropName)
													.join(', ')}`}
										</Typography>
									</Stack>
								))}
							</Stack>
						</Stack>
					)}
					<Grid container spacing={2} marginTop={4}>
						{userActions
							.filter((action) => action.show)
							.map((action, index) => (
								<Grid item xs={12} sm={6} key={index}>
									<Button
										fullWidth
										variant={action.variant as ButtonVariant}
										startIcon={action.icon}
										onClick={action.onClick}
										className={action.className}>
										{action.label}
									</Button>
								</Grid>
							))}
					</Grid>
				</Stack>
			</StyleContainer>
			{showPromoteConfirmation && (
				<Confirmation
					confirmationText={
						<Typography>Are you sure you want to Promote this user?</Typography>
					}
					open={showPromoteConfirmation}
					handleClose={() => setShowPromoteConfirmation(false)}
					handleNoClick={() => setShowPromoteConfirmation(false)}
					handleYesClick={() => promoteMutation.mutate(undefined)}
				/>
			)}
			{showAssignNetworkDialog && (
				<AssignNetworksDialog
					open={showAssignNetworkDialog}
					onClose={() => setShowAssignNetworkDialog(false)}
					unassignedNetworks={unassignedNetworks.data?.data?.networks ?? []}
					selectedUser={selectedUser}
					setShowAssignNetworkDialog={setShowAssignNetworkDialog}
					closeDrawer={closeDrawer}
					onAssignComplete={onAssignComplete}
				/>
			)}
			{showDemoteCsinkManagerDialog && (
				<AssignNetworksForPromoteDemote
					selectedUser={selectedUser}
					closeDrawer={closeDrawer}
					assignNetworksFor='demote'
					onSave={(values) =>
						demoteNetworkAdminToOperator.mutate(
							values as IDemoteCsinkManagerPayload
						)
					}
				/>
			)}
			{showPromoteWithAssignNetworkDialog && (
				<AssignNetworksForPromoteDemote
					selectedUser={selectedUser}
					closeDrawer={closeDrawer}
					assignNetworksFor='promote'
					onSave={(values) => promoteMutation.mutate(values)}
				/>
			)}
		</>
	)
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		width: '100%',
		flexDirection: 'row',
		justifyContent: 'flex-end',
		borderBottom: `1px solid ${theme.palette.divider}`,
		padding: theme.spacing(1.5),
		alignItems: 'center',
		gap: theme.spacing(14),
	},
	'.detail': {
		padding: theme.spacing(2.5),
	},
	'.user-details': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(2),
		alignItems: 'center',
	},
	'.name-title': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(1),
		alignItems: 'center',
	},
	'.user-avatar': {
		width: theme.spacing(11),
		height: theme.spacing(11),
	},
	'.user-info': {
		width: '100%',
		display: ' flex',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		gap: theme.spacing(0.5),
	},
	'.user-contact-info': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		gap: theme.spacing(1),
		fontWeight: 100,
		color: theme.palette.text.secondary,
	},
	'.farm_detail_section': {
		paddingTop: theme.spacing(3),
		gap: theme.spacing(2),
		'.farm_list': {
			rowGap: theme.spacing(3),
			paddingLeft: theme.spacing(3.25),
		},
		'.farm_details': {
			gap: theme.spacing(2),
			'.farm_crops': {
				gap: theme.spacing(1),
				paddingLeft: theme.spacing(3.25),
			},
		},
	},
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
	'.tag_component': {
		flexDirection: 'row',
		columnGap: theme.spacing(1),
		alignItems: 'center',
	},
	'.custom-icon': {
		fontSize: theme.spacing(2.25),
	},
	'.custom-contact-typography': {
		fontSize: theme.spacing(1.75),
		fontWeight: 500,
		marginTop: theme.spacing(0.35),
		maxWidth: theme.spacing(25),
		whiteSpace: 'nowrap',
		overflow: 'hidden',
		textOverflow: 'ellipsis',
	},
	'.button-gray': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.text.primary,
	},
	'.button-red': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.custom.red[700],
	},
	'.button-green': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.custom.green[900],
	},
	'button-red-filled': {
		textTransform: 'capitalize',
	},
	'.assign-network-button': {
		fontSize: theme.spacing(1.6),
		fontWeight: 600,
		padding: '1px 6px',
		borderRadius: theme.spacing(2),
	},
}))
