import { CustomCard, CustomChip } from '@/components'
import { User } from '@/interfaces'
import { DeleteOutline, EmailOutlined } from '@mui/icons-material'
import EditIcon from '@/assets/icons/editIcon.svg'
import {
	Avatar,
	Box,
	IconButton,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import { Stack } from '@mui/material'
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined'
import { userRoles, userRolesNameOrganizationSettings } from '@/utils/constant'

export const UsersItem = ({
	item,
	canEditOrDeleteUser,
	setShowEditUser,
	handleOpenDetails,
	handleDeleteUser,
}: {
	item: User
	canEditOrDeleteUser: (
		value: userRoles,
		isEditButton: boolean,
		userId: string
	) => boolean
	setShowEditUser: React.Dispatch<React.SetStateAction<User | null>>
	handleOpenDetails: (item: User) => void
	handleDeleteUser: (user: User | null) => void
}) => {
	const theme = useTheme()
	return (
		<CustomCard
			onClick={() => handleOpenDetails(item)}
			key={item.id}
			headerComponent={
				<Stack flexDirection={'row'} gap={theme.spacing(2)}>
					<Stack alignSelf={'center'}>
						<Avatar
							src={item?.profileImageUrl?.url}
							sx={{ height: theme.spacing(6.25), width: theme.spacing(6.25) }}
						/>
					</Stack>
					<Stack gap={theme.spacing(0.5)} width={'100%'}>
						<Stack
							direction={'row'}
							justifyContent={'space-between'}
							alignItems={'center'}
							width={'100%'}>
							<Stack direction={'column'} width={'100%'} gap={theme.spacing(0)}>
								<Stack
									direction={'row'}
									justifyContent={'space-between'}
									alignItems={'center'}
									width={'100%'}>
									<Stack
										direction={'row'}
										gap={theme.spacing(1)}
										alignItems={'center'}>
										<Typography
											variant='body1'
											fontWeight={theme.typography.caption.fontWeight}>
											{item.name}
										</Typography>
										<Stack flexDirection={'row'} gap={theme.spacing(1)}>
											<CustomChip
												isSmall
												label={
													userRolesNameOrganizationSettings[
														item?.accountType as userRoles
													]
												}
												appliedClass='lightGrey'
											/>
										</Stack>
									</Stack>
									<Stack direction={'row'} gap={theme.spacing(1)}>
										{canEditOrDeleteUser(
											item?.accountType as userRoles,
											true,
											item.id
										) ? (
											<Tooltip key={item.id} title='Edit' placement='top'>
												<IconButton
													onClick={(e) => {
														e.stopPropagation()
														setShowEditUser(item)
													}}
													sx={{ padding: 0 }}>
													<Box
														component='img'
														src={EditIcon}
														sx={{
															filter: 'grayscale(100%) brightness(0.5)',
														}}
														width={theme.spacing(2.5)}
													/>
												</IconButton>
											</Tooltip>
										) : null}
										{canEditOrDeleteUser(
											item?.accountType as userRoles,
											false,
											item.id
										) ? (
											<Tooltip key={item.id} title='Delete' placement='top'>
												<IconButton
													onClick={(e) => {
														e.stopPropagation()
														handleDeleteUser(item)
													}}
													color='primary'
													sx={{ padding: 0 }}>
													<DeleteOutline sx={{ width: theme.spacing(3) }} />
												</IconButton>
											</Tooltip>
										) : null}
									</Stack>
								</Stack>
								<Stack flexDirection={'column'} gap={theme.spacing(0.25)}>
									{item?.email && (
										<Stack
											flexDirection={'row'}
											alignItems='center'
											gap={theme.spacing(0.5)}>
											<EmailOutlined
												color='disabled'
												sx={{
													width: theme.spacing(2),
													height: theme.spacing(2),
												}}
											/>
											<Typography
												variant='subtitle1'
												fontSize={theme.spacing(1.5)}>
												{item.email}
											</Typography>
										</Stack>
									)}
									{item?.number && (
										<Stack
											flexDirection={'row'}
											alignItems='center'
											gap={theme.spacing(0.5)}>
											<LocalPhoneOutlinedIcon
												color='disabled'
												sx={{
													width: theme.spacing(2),
													height: theme.spacing(2),
												}}
											/>
											<Typography
												variant='subtitle1'
												fontSize={theme.spacing(1.5)}>
												{item?.countryCode ? `(${item.countryCode})` : ''}{' '}
												{item.number}
											</Typography>
										</Stack>
									)}
								</Stack>
							</Stack>
						</Stack>
					</Stack>
				</Stack>
			}
		/>
	)
}
