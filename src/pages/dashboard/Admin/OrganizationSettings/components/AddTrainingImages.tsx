import { Close } from '@mui/icons-material'
import { Divide<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Dispatch, SetStateAction, useEffect } from 'react'
import { styled } from '@mui/material/styles'
import {
	addSupervisorDetailsSchema,
	TAddSupervisorDetailsSchema,
} from './schema'
import { User } from '@/interfaces'
import { SupervisorForm } from './SupervisorForm'
type Props = {
	setShowTrainingImagesDrawer: Dispatch<SetStateAction<boolean>>
	selectedUser: User | null
	closeDrawer: () => void
	demoteUser: (user: TAddSupervisorDetailsSchema) => void
	networks: { id: string; name: string }[]
}

export const AddTrainingImages = ({
	setShowTrainingImagesDrawer,
	selectedUser,
	networks,
	demoteUser,
}: Props) => {
	const form = useForm<TAddSupervisorDetailsSchema>({
		resolver: yupResolver(addSupervisorDetailsSchema),
		mode: 'all',
		defaultValues: {
			trained: false,
			trainingImages: [],
			aadhaarNumber: '',
			aadhaarImage: '',
			profileImage: {},
			networks: [],
		},
	})

	const { setValue, getValues } = form

	console.log({ errors: form.formState?.errors, values: getValues() })

	useEffect(() => {
		if (!selectedUser) return
		setValue('trained', true)
		setValue('trainingImages', selectedUser?.trainingImageUrls || [])
		setValue('profileImage.id', selectedUser?.profileImageUrl?.id || '')
		setValue('profileImage.url', selectedUser?.profileImageUrl?.url || '')
		setValue('aadhaarNumber', selectedUser?.aadhaarNumber || '')
		setValue('aadhaarImage', selectedUser?.aadhaarImageUrl?.id || '')
		setValue(
			'networks',
			networks.map((i) => i.id)
		)
	}, [setValue, selectedUser, networks])

	const onSubmit = () => {
		demoteUser(getValues())
	}

	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h4'>Add Training Images</Typography>
				<IconButton onClick={() => setShowTrainingImagesDrawer(false)}>
					<Close />
				</IconButton>
			</Stack>
			<Divider />
			<SupervisorForm
				form={form}
				showActions
				showUserInfo
				selectedUser={selectedUser}
				onSubmit={onSubmit}
				networks={networks}
				assignNetworks
				onCancel={() => setShowTrainingImagesDrawer(false)}
			/>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2.5),
	},
	'.detail': {
		padding: theme.spacing(2.5),
	},
	'.user-details': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(4),
		alignItems: 'center',
	},
	'.user-avatar': {
		width: theme.spacing(11),
		height: theme.spacing(11),
	},
	'.user-info': {
		display: ' flex',
		flexDirection: 'column',
		gap: theme.spacing(0.5),
	},
	'.user-contact-info': {
		display: 'flex',
		alignItems: 'center',
		gap: theme.spacing(1),
		fontWeight: 100,
		color: theme.palette.text.secondary,
	},
	'.custom-icon': {
		fontSize: theme.spacing(2.25),
	},
	'.custom-contact-typography': {
		fontSize: theme.spacing(1.75),
		fontWeight: 500,
		marginTop: theme.spacing(0.35),
		lineHeight: 0,
	},
}))
