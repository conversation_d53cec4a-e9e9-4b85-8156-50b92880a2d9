import { QueryInput } from '@/components'
import { IImage, IUnassignedNetworks, User } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import {
	AssignNetworkDetails,
	NetworkType,
	userRoles,
	userRolesName,
} from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import { Close, Search } from '@mui/icons-material'
import LanOutlinedIcon from '@mui/icons-material/LanOutlined'

import {
	Box,
	Button,
	Chip,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	FormControlLabel,
	IconButton,
	Radio,
	RadioGroup,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AddSupervisorDetailsDialog } from './AddSupervisorDetailsDialog'

interface AssignNetworksDialogProps {
	open: boolean
	onClose: () => void
	unassignedNetworks: IUnassignedNetworks[]
	selectedUser: User | null
	setShowAssignNetworkDialog: React.Dispatch<React.SetStateAction<boolean>>
	closeDrawer: () => void
	onAssignComplete?: (details: AssignNetworkDetails) => void
}
interface IPayload {
	profileImageId: string | null
	aadhaarNumber?: string | null
	aadhaarImageId: string | null
	trainingImageIds: string[]
	artisanProIds: string[]
	csinkNetworkIds: string[]
	accountType?: string
}
const buildPayload = (
	selectedUser: User | null,
	artisanProIds: string[],
	csinkNetworkIds: string[],
	selectedRole: string,
	supDetails?: {
		profileImage?: { id: string; url?: string }
		trainingImages?: { id: string }[]
		aadhaarNumber?: string | null
		aadhaarImage?: string | null
	}
): IPayload => {
	if (!selectedUser) throw new Error('User not selected')

	return {
		profileImageId:
			supDetails?.profileImage?.id ?? selectedUser?.profileImageUrl?.id ?? null,
		aadhaarNumber: supDetails?.aadhaarNumber ?? selectedUser?.aadhaarNumber,
		aadhaarImageId:
			supDetails?.aadhaarImage ?? selectedUser?.aadhaarImageUrl?.id ?? null,
		trainingImageIds:
			supDetails?.trainingImages?.map((i) => i.id) ??
			selectedUser?.trainingImageUrls?.map((img: IImage) => img.id) ??
			[],
		artisanProIds,
		csinkNetworkIds,
		...(selectedUser?.accountType === userRoles.role_awaiting && {
			accountType: selectedRole,
		}),
	}
}
export const AssignNetworksDialog: React.FC<AssignNetworksDialogProps> = ({
	open,
	onClose,
	unassignedNetworks,
	selectedUser,
	setShowAssignNetworkDialog,
	closeDrawer,
	onAssignComplete,
}) => {
	const [selectedRole, setSelectedRole] = useState<string>(userRoles.Manager)
	const [selectedNetworkIds, setSelectedNetworkIds] = useState<string[]>([])
	const [showDetails, setShowDetails] = useState<boolean>(false)
	const queryClient = useQueryClient()
	const [selectedSearchParams] = useSearchParams()
	const navigate = useNavigate()

	const handleClose = () => {
		selectedSearchParams.delete('userSearch')
		navigate(`?${selectedSearchParams.toString()}`, { replace: true })
		onClose()
	}

	const handleToggleNetwork = (id: string) => {
		setSelectedNetworkIds((prev) =>
			prev.includes(id) ? prev.filter((nId) => nId !== id) : [...prev, id]
		)
	}

	const networkMap = useMemo(() => {
		return unassignedNetworks.reduce((map, network) => {
			map[network.id] = network
			return map
		}, {} as Record<string, IUnassignedNetworks>)
	}, [unassignedNetworks])

	const artisanProIds = selectedNetworkIds.filter(
		(id) => networkMap[id]?.isArtisan
	)
	const csinkNetworkIds = selectedNetworkIds.filter(
		(id) => !networkMap[id]?.isArtisan
	)
	const handleAssignNetworks = useMutation({
		mutationKey: ['assign-user-role'],
		mutationFn: (payload: IPayload) =>
			authAxios.post(`/user/${selectedUser?.id}/assign`, payload),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingUsers'],
			})
			toast.success('network assigned successfully')
			if (
				onAssignComplete &&
				selectedUser?.accountType === userRoles.role_awaiting &&
				selectedRole &&
				selectedNetworkIds.length === 1
			) {
				const networkId = selectedNetworkIds[0]
				const networkName = networkMap[networkId]?.name ?? ''
				const networkType = networkMap[networkId]?.isArtisan
					? NetworkType.artisanPro
					: NetworkType.cSinkNetwork
				onAssignComplete({
					role: selectedRole,
					networks: selectedNetworkIds,
					userType: selectedUser.accountType ?? '',
					networkName,
					networkType,
				})
			}
			selectedSearchParams.delete('userSearch')
			navigate(`?${selectedSearchParams.toString()}`, { replace: true })
			setShowAssignNetworkDialog(false)
			closeDrawer()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleAssignClick = () => {
		if (selectedNetworkIds.length === 0) {
			toast.error('Please select at least one network')
			return
		}

		if (
			selectedUser?.accountType === userRoles.role_awaiting &&
			selectedRole === userRoles.Supervisor
		) {
			setShowDetails(true)
			return
		}

		const payload = buildPayload(
			selectedUser,
			artisanProIds,
			csinkNetworkIds,
			selectedRole
		)
		handleAssignNetworks.mutate(payload)
	}

	return (
		<>
			<Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm'>
				<DialogTitle textAlign='center'>
					<Stack direction='row' alignItems='center' sx={{ width: '100%' }}>
						<Box flex={1} />
						<Typography
							align='center'
							fontWeight={theme.typography.caption.fontWeight}
							fontSize={theme.typography.h6.fontSize}
							color={theme.palette.primary.main}>
							Select Network
						</Typography>
						<Box flex={1} display='flex' justifyContent='flex-end'>
							<IconButton onClick={handleClose}>
								<Close />
							</IconButton>
						</Box>
					</Stack>
					<Stack paddingTop={theme.spacing(2)}>
						<QueryInput
							queryKey='userSearch'
							name='search'
							placeholder='Search by name'
							className='search-textFiled'
							setPageOnSearch
							InputProps={{
								startAdornment: <Search fontSize='small' />,
							}}
						/>
					</Stack>
				</DialogTitle>
				<DialogContent>
					<Stack
						direction='row'
						gap={theme.spacing(3)}
						height={theme.spacing(50)}>
						<Stack
							direction='column'
							gap={theme.spacing(2)}
							padding={theme.spacing(1)}
							flex={1}
							maxHeight='100%'
							overflow='auto'>
							{unassignedNetworks?.length > 0 ? (
								unassignedNetworks.map((item) => {
									const isSelected = selectedNetworkIds.includes(item.id)
									return (
										<StyledItemContainer
											selected={isSelected}
											key={item.id}
											onClick={() => handleToggleNetwork(item.id)}>
											<Stack className='network-item'>
												<LanOutlinedIcon
													color='primary'
													className='entity-icon'
												/>
												<Stack>
													<Stack
														display='flex'
														direction='row'
														gap={theme.spacing(2)}>
														<Typography>{item.name}</Typography>
														<Chip
															label={
																item.isArtisan ? 'Artisan pro' : 'Csink Network'
															}
															size='small'
															sx={{
																fontSize: theme.spacing(1.75),
																backgroundColor: 'white',
																border: `1px solid ${theme.palette.divider}`,
															}}
														/>
													</Stack>
													<Typography fontSize={theme.spacing(1.75)}>
														{item.address}
													</Typography>
												</Stack>
											</Stack>
										</StyledItemContainer>
									)
								})
							) : (
								<Typography textAlign='center'>No Networks Found</Typography>
							)}
						</Stack>

						{selectedUser?.accountType === userRoles.role_awaiting && (
							<Stack
								minWidth={theme.spacing(25)}
								paddingLeft={theme.spacing(2)}
								alignItems='center'
								justifyContent='center'>
								<Typography mb={1}>Select the role for this user</Typography>
								<RadioGroup
									value={selectedRole}
									onChange={(e) => setSelectedRole(e.target.value)}>
									<FormControlLabel
										value={userRoles.Manager}
										control={
											<Radio
												sx={{
													transform: 'scale(0.8)',
													padding: theme.spacing(0.5),
												}}
											/>
										}
										label={userRolesName.manager}
									/>
									<FormControlLabel
										value={userRoles.Supervisor}
										control={
											<Radio
												sx={{
													transform: 'scale(0.8)',
													padding: theme.spacing(0.5),
												}}
											/>
										}
										label={userRolesName.supervisor}
									/>
								</RadioGroup>
							</Stack>
						)}
					</Stack>
				</DialogContent>

				<DialogActions
					sx={{
						px: theme.spacing(4),
						py: theme.spacing(2),
						justifyContent: 'center',
					}}>
					<Button
						variant='contained'
						sx={{ width: '30%' }}
						onClick={handleAssignClick}>
						Assign
					</Button>
				</DialogActions>
			</Dialog>

			{showDetails && (
				<AddSupervisorDetailsDialog
					open={showDetails}
					onClose={() => setShowDetails(false)}
					onSubmit={(supDetails) => {
						const payload = buildPayload(
							selectedUser,
							artisanProIds,
							csinkNetworkIds,
							selectedRole,
							supDetails
						)
						handleAssignNetworks.mutate(payload)
						setShowDetails(false)
						onClose()
					}}
				/>
			)}
		</>
	)
}

interface StyledItemProps {
	selected: boolean
}
const StyledItemContainer = styled(Stack, {
	shouldForwardProp: (prop) => prop !== 'selected',
})<StyledItemProps>(({ theme, selected }) => ({
	flexDirection: 'row',
	alignItems: 'center',
	padding: theme.spacing(1),
	gap: theme.spacing(2),
	backgroundColor: selected ? theme.palette.action.selected : 'transparent',
	'&:hover': {
		backgroundColor: selected
			? theme.palette.action.selected
			: theme.palette.action.hover,
	},
	'.entity-icon': {
		width: theme.spacing(5),
		height: theme.spacing(5),
		backgroundColor: theme.palette.custom.red[300],
		padding: theme.spacing(0.5),
		borderRadius: theme.spacing(0.75),
	},
	'.network-item': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(2),
		alignItems: 'center',
	},
}))
