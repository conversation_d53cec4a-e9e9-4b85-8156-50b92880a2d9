import { authAxios, useAuthContext } from '@/contexts'
import {
	EntitiesResponse,
	IArtisanProDetails,
	ManagerDetail,
	EntityEnum,
	IUsersResponse,
	ProfileImageURL,
	UnassignedUser,
	User,
	IEntitytabsCount,
	ICommonUser,
} from '@/interfaces'
import { IUnassignedUsersResponse } from '@/interfaces'
import {
	AssignNetworkDetails,
	defaultLimit,
	defaultPage,
	entitiesRoles,
	NetworkType,
	userRoles,
} from '@/utils/constant'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { showAxiosErrorToast } from '@/utils/helper'
import { AxiosError } from 'axios'
import { SupervisorDetailsData } from './components/AddSupervisorDetailsDialog'

export enum filterUserTypes {
	all = '',
	admin = 'admin',
	role_awaiting = 'roleAwaiting',
	compliance_manager = 'complianceManager',
	circonomy_employee = 'circonomyEmployee',
	ceres_auditors = 'ceres_auditor',
	csink_managers = 'cSinkManager',
	network_admins = 'networkAdmin',
	operators = 'operator',
	farmers = 'farmer',
}

export const convertNetworkManagerToUserType = (
	manager: ManagerDetail,
	entityId: string,
	entityName?: string,
	csinkManagerName?: string
): User => {
	const isArtisanProOrManager = [
		userRoles.ArtisanPro,
		userRoles.Manager,
	].includes(manager.accountType as userRoles)
	const isCSinkNetworkOrManager = [
		userRoles.cSinkNetwork,
		userRoles.Manager,
	].includes(manager.accountType as userRoles)
	return {
		id: manager.managerId,
		name: manager.managerName,
		email: manager.managerEmail,
		number: manager.managerPhone,
		countryCode: manager.countryCode,
		accountType: manager.accountType,
		profileImageUrl: manager.profileImageUrl,
		trainingImageUrls: manager.trainingImages as ProfileImageURL[],
		aadhaarImageUrl: null,
		aadhaarNumber: null,
		csinkManagerId: manager.csinkManagerId,
		csinkManagerName: [
			userRoles.CsinkManager,
			userRoles.role_awaiting,
		].includes(manager.accountType as userRoles)
			? entityName
			: csinkManagerName,
		csinkManagerShortName: '',
		biomassAggregatorId: '',
		biomassAggregatorName: '',
		biomassAggregatorShortName: '',
		artisanProNetworkId: '',
		artisanProNetworkName: '',
		artisanProNetworkShortName: '',
		artisanProId: isArtisanProOrManager ? entityId : '',
		artisanProName: isArtisanProOrManager ? entityName : '',
		artisanProShortName: '',
		csinkNetworkId: isCSinkNetworkOrManager ? entityId : '',
		csinkNetworkName: isCSinkNetworkOrManager ? entityName : '',
		csinkNetworkShortName: null,
		artisanPros: [],
		csinkNetworks: [],
		address: manager.managerAddress,
		pendingUploadsCount: manager.pendingUploadsCount || 0,
		pendingActionCount: 0,
		certificateDetails: manager?.certificateUrl || null,
		gender: undefined,
		cropId: undefined,
		landmark: undefined,
	}
}

// as we need to convert this CommonUser type to User Type
export const convertToUserType = (
	item: ICommonUser,
	entityId: string,
	entityName: string,
	csinkManagerName: string
): User => {
	const isArtisanProOrManager = [
		userRoles.ArtisanPro,
		userRoles.Manager,
	].includes(item.accountType as userRoles)
	const isCSinkNetworkOrManager = [
		userRoles.cSinkNetwork,
		userRoles.Manager,
	].includes(item.accountType as userRoles)
	return {
		id: item.id,
		name: item.name,
		email: item.email || '',
		number: item.phoneNo || item.number || '',
		countryCode: item.countryCode,
		accountType: item.accountType || '',
		profileImageUrl: item.profileImageUrl,
		trainingImageUrls: item.trainingImages as ProfileImageURL[],
		aadhaarImageUrl: null,
		aadhaarNumber: null,
		csinkManagerId: '',
		csinkManagerName: csinkManagerName,
		csinkManagerShortName: '',
		biomassAggregatorId: '',
		biomassAggregatorName: '',
		biomassAggregatorShortName: '',
		artisanProNetworkId: '',
		artisanProNetworkName: '',
		artisanProNetworkShortName: '',
		artisanProId: isArtisanProOrManager ? entityId : '',
		artisanProName: isArtisanProOrManager ? entityName : '',
		artisanProShortName: '',
		csinkNetworkId: isCSinkNetworkOrManager ? entityId : '',
		csinkNetworkName: isCSinkNetworkOrManager ? entityName : '',
		csinkNetworkShortName: null,
		artisanPros: [],
		csinkNetworks: [],
		address: '',
		pendingUploadsCount: 0,
		pendingActionCount: 0,
		certificateDetails: item?.certificateUrl || null,
		gender: undefined,
		cropId: undefined,
		landmark: undefined,
	}
}

const fetchUsersFn = async (
	search: string,
	limit: string,
	page: string,
	userTypes: string[]
) => {
	const queryParams = new URLSearchParams({
		limit,
		page,
		search,
		userType: userTypes.join(','),
	})
	const apiUrl = '/new/users/v2'
	return authAxios.get<IUsersResponse>(`${apiUrl}?${queryParams.toString()}`)
}

const fetchEntityUsersFn = async (
	entityType: string,
	entityId: string,
	search: string,
	limit: string,
	page: string
) => {
	const queryParams = new URLSearchParams({
		limit,
		page,
		search,
	})
	const apiUrl = `/${entityType}/${entityId}`
	return authAxios.get<IArtisanProDetails>(
		`${apiUrl}?${queryParams.toString()}`
	)
}

const fetchEntitiesFn = async (
	search: string,
	limit: string,
	page: string,
	entityTypes: string[],
	status: string
) => {
	const queryParams = new URLSearchParams({
		limit,
		page,
		search,
		entity_type: entityTypes.join(','),
		status,
	})
	const apiUrl = '/new/entities'
	return authAxios.get<EntitiesResponse>(`${apiUrl}?${queryParams.toString()}`)
}

const fetchUnassignedUsersFn = async (
	managerId: string,
	entityId: string,
	selectedEntitieType: string
) => {
	const queryParams = new URLSearchParams({
		csinkManagerId: managerId,
	})
	let apiUrl: string
	if (selectedEntitieType === 'cs-network') {
		apiUrl = `/cs-network/${entityId}/unassigned-users`
	} else if (selectedEntitieType === 'artisian-pro') {
		apiUrl = `/artisian-pro/${entityId}/unassigned-users`
	} else {
		apiUrl = `/drop-down/unassigned-users?${queryParams.toString()}`
	}
	return authAxios.get<IUnassignedUsersResponse>(apiUrl)
}

const assignUserFn = async (
	user: UnassignedUser | null,
	selectedEntitieType: string,
	entityId: string,
	supDetails?: SupervisorDetailsData,
	selectedRole?: userRoles
) => {
	if (!user) {
		throw new Error('User not found in unassigned list')
	}
	const payload = {
		profileImageId: supDetails?.profileImage?.id ?? null,
		aadhaarNumber: supDetails?.aadhaarNumber ?? null,
		aadhaarImageId: supDetails?.aadhaarImage ?? null,
		trainingImageIds: supDetails?.trainingImages?.map((i) => i.id) ?? [],
		artisanProIds: selectedEntitieType == 'artisian-pro' ? [entityId] : null,
		csinkNetworkIds: selectedEntitieType == 'cs-network' ? [entityId] : null,
		...(user?.accountType === userRoles.role_awaiting && {
			accountType: selectedRole,
		}),
	}
	return authAxios.post(`/user/${user?.id}/assign`, payload)
}

const getToggleSuspendUrl = (
	tab: string,
	id: string,
	isSuspend: boolean
): string => {
	switch (tab) {
		case entitiesRoles.CsinkManager:
			return `/csink-manager/${id}/toggle-suspend?isSuspend=${isSuspend}`
		case entitiesRoles.cSinkNetwork:
			return `/cs-network/${id}/toggle-suspend?isSuspend=${isSuspend}`
		case entitiesRoles.ArtisanPro:
			return `/artisian-pro/${id}/toggle-suspend?isSuspend=${isSuspend}`
		default:
			return `/csink-manager/${id}/toggle-suspend?isSuspend=${isSuspend}`
	}
}

const suspendUserFn = async ({
	id,
	isSuspend,
	tab,
}: {
	id: string
	isSuspend: boolean
	tab: string
}) => {
	const { data } = await authAxios.patch(
		getToggleSuspendUrl(tab, id, isSuspend)
	)
	return data
}

const removeUserFn = async (selectedUser: User | null) => {
	if (!selectedUser) throw new Error('User data is missing')

	let endpoint = ''
	switch (selectedUser.accountType) {
		case userRoles.cSinkNetwork:
		case userRoles.csinkNetworkOperator:
		case userRoles.artisanProOperator:
		case userRoles.Manager:
		case userRoles.ArtisanPro:
			endpoint = `/user/${selectedUser?.id}`
			break
		case userRoles.CsinkManager:
			endpoint = `/csink-manager/user/${selectedUser.id}`
			break
		case userRoles.BiomassAggregator:
			endpoint = `/biomass-aggregator/manager/${selectedUser.id}`
			break

		case userRoles.artisanProNetworkManager:
			endpoint = `/artisan-pro-network/${selectedUser.artisanProNetworkId}/manager/${selectedUser.id}`
			break
		default:
			throw new Error('Unsupported account type')
	}

	const { data } = await authAxios.delete(endpoint)
	return data
}

export const useOrganizationSettings = () => {
	const queryClient = useQueryClient()
	const [searchParams, setSearchParams] = useSearchParams()
	const [showAddUser, setShowAddUser] = useState<boolean>(false)
	const [showAddFarmer, setShowAddFarmer] = useState<boolean>(false)
	const [showEditEntities, setShowEditEntities] = useState<string | null>(null)
	const [showEditUser, setShowEditUser] = useState<User | null>(null)

	const [showAssignUserDialog, setShowAssignUserDialog] =
		useState<boolean>(false)
	const [showAddEntity, setShowAddEntity] = useState<boolean>(false)
	const [selectedEntity, setSelectedEntity] = useState<string>('')
	const search = searchParams.get('search') || ''
	const [selectedUserForAssign, setSelectedUserForAssign] =
		useState<UnassignedUser | null>(null)
	const UnassignedUsersSearch = searchParams.get('userSearch') || ''
	const entitieTypes = searchParams.getAll('types') || []
	const userTypes = searchParams.getAll('userRole') || []
	const selectedEntitieTypeParam = searchParams.get('selectedEntityType')

	const entitieId = searchParams.get('entityId') || ''
	const editEntityId = searchParams.get('editEntityId') || ''
	const paramsUsersLimit =
		searchParams.get('userLimit') || defaultLimit.toString()
	const paramsUsersPage = searchParams.get('userPage') || defaultPage.toString()
	const paramsEntitieLimit =
		searchParams.get('entityLimit') || defaultLimit.toString()
	const paramsEntitiePage =
		searchParams.get('entityPage') || defaultPage.toString()
	const entitieStatus = searchParams.get('entityStatus') || ''
	const [isUserDetailInfoDrawer, setIsUserDetailInfoDrawer] = useState(false)
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<boolean>(false)
	const [showBiomassReferenceDialog, setShowBiomassReferenceDialog] = useState<{
		id: string
		type: EntityEnum
	} | null>(null)
	const [assignDetails, setAssignDetails] =
		useState<AssignNetworkDetails | null>(null)
	const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)

	const { userDetails } = useAuthContext()
	const navigate = useNavigate()

	const showAllUsersList = useMemo(() => {
		if (entitieId) return false
		else return true
	}, [entitieId])

	const selectedEntitieType = useMemo(() => {
		let type = null
		if (selectedEntitieTypeParam) {
			if (selectedEntitieTypeParam === 'csink_manager') type = 'csink-manager'
			else if (selectedEntitieTypeParam === 'artisan_pro') type = 'artisian-pro'
			else if (selectedEntitieTypeParam === 'csink_network') type = 'cs-network'
		}
		return type
	}, [selectedEntitieTypeParam])

	const userFiltersOption = useMemo(
		() => [
			{
				label: 'All',
				value: filterUserTypes.all,
			},
			...([userRoles.Admin]?.includes(userDetails?.accountType as userRoles)
				? [
						{
							label: 'Admins',
							value: filterUserTypes.admin,
						},
						{
							label: 'Role Awaiting',
							value: filterUserTypes.role_awaiting,
						},
						{
							label: 'Compliance Manager',
							value: filterUserTypes.compliance_manager,
						},
						{
							label: 'Circonomy Employee',
							value: filterUserTypes.circonomy_employee,
						},
						{
							label: 'CERES Auditor',
							value: filterUserTypes.ceres_auditors,
						},
				  ]
				: []),

			...([userRoles.Admin, userRoles.CsinkManager]?.includes(
				userDetails?.accountType as userRoles
			)
				? [
						{
							label: 'Company Admin',
							value: filterUserTypes.csink_managers,
						},
				  ]
				: []),

			{
				label: 'Manager',
				value: filterUserTypes.network_admins,
			},
			{
				label: 'Supervisor',
				value: filterUserTypes.operators,
			},
			{
				label: 'Farmer',
				value: filterUserTypes.farmers,
			},
		],
		[userDetails?.accountType]
	)

	const canEditOrDeleteUser = useCallback(
		(userRole: userRoles, isEditButton: boolean) => {
			switch (userDetails?.accountType) {
				case userRoles.Admin: {
					return [
						...(isEditButton ? [userRoles.ceres_auditor] : []),
						userRoles.CsinkManager,
						userRoles.ArtisanPro,
						userRoles.cSinkNetwork,
						userRoles.Manager,
						userRoles.artisanProOperator,
						userRoles.csinkNetworkOperator,
						userRoles.Supervisor,
					].includes(userRole)
				}
				case userRoles.CsinkManager:
					return [
						userRoles.CsinkManager,
						userRoles.ArtisanPro,
						userRoles.cSinkNetwork,
						userRoles.Manager,
						userRoles.artisanProOperator,
						userRoles.csinkNetworkOperator,
						userRoles.Supervisor,
					].includes(userRole)
				case userRoles.cSinkNetwork:
					return (
						userRole === userRoles.csinkNetworkOperator ||
						userRole === userRoles.Supervisor
					)

				case userRoles.ArtisanPro:
					return (
						userRole === userRoles.artisanProOperator ||
						userRole === userRoles.Supervisor
					)
				case userRoles.Manager:
					return [
						userRoles.artisanProOperator,
						userRoles.csinkNetworkOperator,
						userRoles.Supervisor,
					].includes(userRole)
				default:
					return false
			}
		},
		[userDetails?.accountType]
	)

	const fetchUsers = useQuery({
		queryKey: [
			'OrganizationSettingUsers',
			search,
			paramsUsersLimit,
			paramsUsersPage,
			userTypes,
		],
		queryFn: () =>
			fetchUsersFn(search, paramsUsersLimit, paramsUsersPage, userTypes),
		enabled: true,
	})

	const fetchEntityUsers = useQuery({
		queryKey: [
			'OrganizationSettingEntityUsers',
			search,
			paramsUsersLimit,
			paramsUsersPage,
			selectedEntitieType,
			entitieId,
		],
		queryFn: () =>
			fetchEntityUsersFn(
				selectedEntitieType!,
				entitieId!,
				search,
				paramsUsersLimit,
				paramsUsersPage
			),
		enabled: !!selectedEntitieType && !!entitieId,
	})

	const fetchEntities = useQuery({
		queryKey: [
			'entities',
			search,
			paramsEntitieLimit,
			paramsEntitiePage,
			entitieTypes,
			entitieStatus,
		],
		queryFn: () =>
			fetchEntitiesFn(
				search,
				paramsEntitieLimit,
				paramsEntitiePage,
				entitieTypes,
				entitieStatus
			),
		enabled: true,
	})
	const entityCountQuery = useQuery({
		queryKey: ['entityTabsCountQuery'],
		queryFn: async () => {
			const { data } = await authAxios.get<IEntitytabsCount>(
				`/get-entities-count`
			)
			return data
		},
	})
	const currentCSinkMangerId = useMemo(() => {
		return (
			fetchEntities?.data?.data?.entities?.find((item) => item.id === entitieId)
				?.csinkManagerId ?? ''
		)
	}, [entitieId, fetchEntities?.data?.data?.entities])

	const { data: unassignedUsers, refetch: refetchUnassignedUsers } = useQuery({
		queryKey: ['fetchUnassignedUsers', UnassignedUsersSearch],
		queryFn: () =>
			fetchUnassignedUsersFn(
				currentCSinkMangerId,
				entitieId,
				selectedEntitieType ?? ''
			),
		enabled: !!entitieId,
	})

	useEffect(() => {
		if (showAssignUserDialog) {
			refetchUnassignedUsers()
		}
	}, [refetchUnassignedUsers, showAssignUserDialog])

	// const accountType = useMemo(() => {
	// 	switch (selectedEntitieType) {
	// 		case 'csink-manager':
	// 			return 'c_sink_manager'
	// 		case 'artisian-pro':
	// 		case 'cs-network':
	// 			return 'manager'
	// 		default:
	// 			return ''
	// 	}
	// }, [selectedEntitieType])

	const assignUserMutation = useMutation({
		mutationKey: ['assign-user-role'],
		mutationFn: ({
			user,
			entityType,
			entityId,
			supDetails,
			selectedRole,
		}: {
			user: UnassignedUser
			entityType: string
			entityId: string
			supDetails?: SupervisorDetailsData
			selectedRole?: userRoles
		}) => assignUserFn(user, entityType, entityId, supDetails, selectedRole),
		onSuccess: (_data, variables) => {
			const { selectedRole } = variables
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
			toast.success('User assigned successfully')
			searchParams.delete('userSearch')
			navigate(`?${searchParams.toString()}`, { replace: true })

			setSelectedUserForAssign(null)
			setShowAssignUserDialog(false)

			handleAssignComplete({
				role: selectedRole as string,
				networks: [entitieId],
				userType: '',
				networkName: fetchEntities?.data?.data?.entities.find(
					(item) => item.id === entitieId
				)?.name,
				networkType:
					selectedEntitieType === 'artisian-pro'
						? NetworkType.artisanPro
						: NetworkType.cSinkNetwork,
			})
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleAssignUser = ({
		supDetails,
		selectedRole,
	}: {
		supDetails?: SupervisorDetailsData
		selectedRole?: userRoles
	}) => {
		if (selectedUserForAssign) {
			assignUserMutation.mutate({
				user: selectedUserForAssign,
				entityType: selectedEntitieType ?? '',
				entityId: entitieId,
				supDetails,
				selectedRole,
			})
		} else toast('Select User to assign')
	}

	const useSuspendMutation = () =>
		useMutation({
			mutationFn: suspendUserFn,
			onSuccess: (data) => {
				toast(data.message)
				queryClient.invalidateQueries({ queryKey: ['entities'] })
			},
			onError: (error: AxiosError) => {
				showAxiosErrorToast(error)
			},
		})

	const suspendMutation = useSuspendMutation()

	const handleSuspendEntity = useCallback(
		(id: string, isSuspend: boolean, tab: string) => {
			suspendMutation.mutate({
				id,
				isSuspend,
				tab,
			})
		},
		[suspendMutation]
	)

	const removeUserMutation = useMutation({
		mutationKey: ['removeUser'],
		mutationFn: removeUserFn,
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'User removed successfully')
			if (entitieId) {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			} else {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			}
			setIsUserDetailInfoDrawer(false)
			setShowConfirmationDialog(false)
		},
	})

	const handleRemoveUser = (selectedUser: User | null) => {
		removeUserMutation.mutate(selectedUser)
	}

	const handleAssignComplete = (details: AssignNetworkDetails) => {
		setAssignDetails(details)
		setConfirmDialogOpen(true)
	}

	return {
		usersList: fetchUsers?.data?.data?.users ?? [],
		unassignedUsersList: unassignedUsers?.data?.unassignedUsers ?? [],
		entityUsers: fetchEntityUsers?.data?.data,
		entityUsersCount: fetchEntityUsers?.data?.data?.managerDetails?.length ?? 0,
		isEntityUsersLoading: fetchEntityUsers?.isFetching,
		totalUsersCount: fetchUsers?.data?.data?.count ?? 0,
		isUsersLoading: fetchUsers?.isFetching,
		entitiesList: fetchEntities?.data?.data?.entities ?? [],
		totalEntitiesCount: fetchEntities?.data?.data?.count ?? 0,
		isEntitiesLoading: fetchEntities?.isFetching,
		fetchEntities,
		cSinkNetworkCount: entityCountQuery?.data?.csinkNetworkCount ?? 0,
		setSearchParams,
		showAddUser,
		setShowAddUser,
		setShowAssignUserDialog,
		showAssignUserDialog,
		selectedUserForAssign,
		setSelectedUserForAssign,
		handleAssignUser,
		userFiltersOption,
		showAllUsersList,
		handleSuspendEntity,
		showEditEntities,
		setShowEditEntities,
		editEntityId,
		showAddEntity,
		setShowAddEntity,
		selectedEntity,
		setSelectedEntity,
		canEditOrDeleteUser,
		showEditUser,
		setShowEditUser,
		handleRemoveUser,
		isUserDetailInfoDrawer,
		setIsUserDetailInfoDrawer,
		showConfirmationDialog,
		setShowConfirmationDialog,
		showBiomassReferenceDialog,
		setShowBiomassReferenceDialog,
		showAddFarmer,
		setShowAddFarmer,
		assignDetails,
		handleAssignComplete,
		confirmDialogOpen,
		setConfirmDialogOpen,
	}
}
