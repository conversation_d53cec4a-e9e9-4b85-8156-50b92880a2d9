import {
	alpha,
	Box,
	Card,
	Chip,
	CircularProgress,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { useMemo, useState } from 'react'

import { useHome } from './useHome'
import { GoogleMapWithMultipleMarker } from '@/components/GoogleMap'
import { MeasuringUnits, NetworkEnumForHomePage } from '@/types'
import { CustomHeader } from '@/components'
import { useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import { NewChartCard } from './NewChartCard.tsx'
import { BiomassFilter } from '../Production/Biomass/BiomassFilters'
import { convertLitreToMeterCube } from '@/utils/helper/measuringCalculations.helper.ts'
import { theme } from '@/lib/theme/theme.ts'
import { TimefilterHomeScreen } from './TimefilterHomeScreen.tsx'

const measuringUnitOptions = [
	{
		label: 'm³',
		value: MeasuringUnits.m3,
	},
	{
		label: 'Ltr',
		value: MeasuringUnits.ltr,
	},
	{
		label: 'ton',
		value: MeasuringUnits.ton,
	},
]

const fixedDecimal = (value: number, decimalPlaces: number): number => {
	let suffix = ''
	const decimals = value.toString().split('.')

	if (decimals?.[1]) {
		suffix = decimals[1].slice(0, decimalPlaces)
	}

	const result = suffix ? `${decimals[0]}.${suffix}` : decimals[0]

	return Number(result)
	// const factor = Math.pow(10, decimalPlaces)
	// return Math.trunc(value * factor) / factor
}
const convertBiocharValue = (
	valueInLtr: number,
	valueTonn: number,
	unit: string
): number => {
	if (unit === MeasuringUnits.m3)
		return fixedDecimal(convertLitreToMeterCube(valueInLtr), 3)
	if (unit === MeasuringUnits.ltr) return fixedDecimal(valueInLtr, 3)
	return fixedDecimal(valueTonn, 3)
}
const defaultColors = [
	theme.palette.custom.green[700],
	theme.palette.custom.yellow[500],
	theme.palette.custom.red[500],
	theme.palette.custom.red[600],
	theme.palette.custom.yellow[200],
]

const pendingColors = [
	theme.palette.custom.green[700],
	theme.palette.custom.yellow[500],
	theme.palette.custom.red[600],
	theme.palette.custom.red[500],
	theme.palette.custom.yellow[200],
]
export const Home = () => {
	const theme = useTheme()
	const {
		fetchQuantityData,
		fetchSites,
		fetchAssets,
		handleSubNetwork,
		searchParams,
		subTabOptions,
		fetchArtisanProsAndCSinkNetworks,
		handleChipFilter,
		mapCenter,
		currentLocationQuery,
	} = useHome()
	const paramsSubNetwork =
		searchParams.get('subNetwork') || NetworkEnumForHomePage.all
	const showSiteOrKilnParams = searchParams.get('showSitesOrKilns') || 'false'
	const withLabelParams = searchParams.get('withLabel') || 'true'

	const { userDetails } = useAuthContext()

	const [biocharUnit, setBiocharUnit] = useState<MeasuringUnits>(
		MeasuringUnits.m3
	)
	const handleBiocharUnit = (value: MeasuringUnits) => {
		setBiocharUnit(value)
	}

	const unMixed =
		(fetchQuantityData?.data?.totalBiocharQuantity || 0) -
		(fetchQuantityData?.data?.mixedBiocharQuantity || 0)

	const lostCarbonCredits =
		parseFloat(
			((fetchQuantityData?.data?.rejectedCarbonCredits || 0) / 1000)?.toFixed(2)
		) || 0

	const rejectedBiocharQuantity =
		fetchQuantityData?.data?.rejectedBiocharQuantity || 0

	const mixing =
		parseFloat(
			(
				(fetchQuantityData?.data?.pendingApplicationCarbonCredits || 0) / 1000
			)?.toFixed(2)
		) || 0

	const pendingRegistrationCarbonCredits =
		parseFloat(
			(
				(fetchQuantityData?.data?.pendingRegistrationCarbonCredits ?? 0) / 1000
			)?.toFixed(2)
		) || 0

	const pendingSinkCarbonCredits =
		parseFloat(
			(
				(fetchQuantityData?.data?.pendingSinkCarbonCredits ?? 0) / 1000
			)?.toFixed(2)
		) || 0

	const totalPending =
		parseFloat(
			(
				((fetchQuantityData?.data?.pendingRegistrationCarbonCredits ?? 0) +
					(fetchQuantityData?.data?.pendingApplicationCarbonCredits ?? 0) +
					(fetchQuantityData?.data?.pendingSinkCarbonCredits ?? 0)) /
				1000
			)?.toFixed(2)
		) || 0

	const mixed = fetchQuantityData?.data?.mixedBiocharQuantity ?? 0

	const Registered = fetchQuantityData?.data?.approvedSinkCarbonCredits
		? parseFloat(
				(
					(fetchQuantityData?.data?.approvedSinkCarbonCredits ?? 0) / 1000
				)?.toFixed(2)
		  ) || 0
		: 0

	const Compensated = fetchQuantityData?.data?.compensatedCarbonCredits
		? parseFloat(
				(fetchQuantityData?.data?.compensatedCarbonCredits / 1000)?.toFixed(2)
		  ) || 0
		: 0

	const mixedTonn = fetchQuantityData?.data?.mixedBiocharQuantityInTonnes ?? 0

	const unMixedTonn =
		(fetchQuantityData?.data?.totalBiocharQuantityinTonnes || 0) -
			(fetchQuantityData?.data?.mixedBiocharQuantityInTonnes || 0) || 0

	const rejectedBiocharQuantityTonn =
		fetchQuantityData?.data?.rejectedBiocharQuantityInTonnes ?? 0
	const chartData = useMemo(
		() => (id: string) => {
			return {
				series: [4000, 3000],
				options: {
					legend: { show: false },
					dataLabels: { enabled: false },
					tooltip: {
						enabled: true,
						fillSeriesColor: false,
						marker: { show: false },
						y: {
							formatter: function (val: number) {
								if (id === 'biochar') return val + ' ' + biocharUnit
								else if (id === 'pending') return val + ' tCO₂'
								else if (id === 'credit') return val + ' tCO₂'
							},
							title: {
								formatter: function () {
									return ''
								},
							},
						},
					},
					responsive: [
						{
							breakpoint: 680,
							options: {
								chart: {
									width: 140,
								},
							},
						},
						{
							breakpoint: 1420,
							options: {
								chart: {
									width: 190,
								},
							},
						},
					],
					// we conditionally change the order of the color as in case of pending biochar credits we have to show pink color first than red color
					fill: {
						colors: id === 'pending' ? pendingColors : defaultColors,
					},
					states: {
						hover: { filter: { type: 'darken', value: 0.5 } },
						active: { filter: { type: 'none', value: 0 } },
					},
					stroke: { width: 0 },
				},
			}
		},
		[biocharUnit]
	)

	const mapMarkers = useMemo(() => {
		if (!!paramsSubNetwork && showSiteOrKilnParams === 'false') {
			return fetchArtisanProsAndCSinkNetworks?.data
		}
		if (showSiteOrKilnParams === 'true') {
			return fetchSites?.data
		}
	}, [
		fetchArtisanProsAndCSinkNetworks?.data,
		fetchSites?.data,
		paramsSubNetwork,
		showSiteOrKilnParams,
	])

	const totalQuantities = useMemo(() => {
		const quantities = []

		if (
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.ArtisanPro,
				userRoles.Manager,
				userRoles.artisanProNetworkManager,
			].includes(userDetails?.accountType as userRoles)
		) {
			quantities.push({
				label: 'Total Artisan Pro:',
				value: fetchAssets?.data?.artisanPros,
			})
		}

		if (
			[
				userRoles.Admin,
				userRoles.cSinkNetwork,
				userRoles.CsinkManager,
				userRoles.Manager,
				userRoles.BiomassAggregator,
			].includes(userDetails?.accountType as userRoles)
		) {
			quantities.push({
				label: 'Total C-Sink Network:',
				value: fetchAssets?.data?.cSinkNetworks,
			})
		}

		quantities.push(
			{
				label: 'Total Site:',
				value: fetchAssets?.data?.sites,
			},
			{
				label: 'Total Kilns:',
				value: fetchAssets?.data?.kilns,
			},
			{
				label: 'Total Farmers:',
				value: fetchAssets?.data?.farmers,
			},
			{
				label: 'Total Biomass:',
				value: (Number(fetchAssets?.data?.biomassCollected) / 1000)?.toFixed(2),
				unit: ' ton',
			}
		)

		return quantities
	}, [
		userDetails?.accountType,
		fetchAssets?.data?.artisanPros,
		fetchAssets?.data?.biomassCollected,
		fetchAssets?.data?.cSinkNetworks,
		fetchAssets?.data?.farmers,
		fetchAssets?.data?.kilns,
		fetchAssets?.data?.sites,
	])

	const chipData = [
		{
			label: 'Sites/Kilns',
			filterKey: 'showSitesOrKilns',
			param: showSiteOrKilnParams,
		},
		{ label: 'With Label', filterKey: 'withLabel', param: withLabelParams },
	]

	const ChartLabelDataCollection = useMemo(() => {
		return [
			{
				id: 'credit',
				label: 'Total Credits',
				unit: 'tCO₂',
				allData: [
					{
						labelName: 'Registered',
						labelQuantity: Registered,
						color: theme.palette.success.main,
					},
					{
						labelName: 'Pending',
						labelQuantity: totalPending,
						color: theme.palette.warning.main,
					},
					{
						labelName: 'Lost',
						labelQuantity: lostCarbonCredits,
						color: theme.palette.custom.red[500],
					},
					{
						labelName: 'Compensated',
						labelQuantity: Compensated,
						color: theme.palette.custom.yellow[200],
					},
				],
			},
			{
				id: 'pending',
				label: 'Pending biochar credits',
				unit: 'tCO₂',
				allData: [
					{
						labelName: 'Mixing/Application',
						labelQuantity: mixing,
						color: theme.palette.success.main,
					},
					{
						labelName: 'Awaiting Registration',
						labelQuantity: pendingRegistrationCarbonCredits,
						color: theme.palette.warning.main,
					},
					{
						labelName: 'Pending on CSI',
						labelQuantity: pendingSinkCarbonCredits,
						color: theme.palette.custom.red[600],
					},
				],
			},
			{
				id: 'biochar',
				label: 'Biochar Details',
				unit: biocharUnit,
				allData: [
					{
						labelName: 'Mixed',
						labelQuantity: convertBiocharValue(mixed, mixedTonn, biocharUnit),
						color: theme.palette.success.main,
					},
					{
						labelName: 'Not mixed',
						labelQuantity: convertBiocharValue(
							unMixed,
							unMixedTonn,
							biocharUnit
						),
						color: theme.palette.warning.main,
					},
					{
						labelName: 'Rejected',
						labelQuantity: convertBiocharValue(
							rejectedBiocharQuantity,
							rejectedBiocharQuantityTonn,
							biocharUnit
						),
						color: theme.palette.custom.red[500],
					},
				],
			},
		]
	}, [
		Registered,
		theme.palette.success.main,
		theme.palette.warning.main,
		theme.palette.custom.red,
		theme.palette.custom.yellow,
		totalPending,
		lostCarbonCredits,
		Compensated,
		mixing,
		pendingRegistrationCarbonCredits,
		pendingSinkCarbonCredits,
		biocharUnit,
		mixed,
		mixedTonn,
		unMixed,
		unMixedTonn,
		rejectedBiocharQuantity,
		rejectedBiocharQuantityTonn,
	])
	const showCsinkManagerFilter = [userRoles.Admin].includes(
		userDetails?.accountType as userRoles
	)

	return (
		<CustomeHome>
			<CustomHeader
				showBottomBorder={false}
				heading='Home'
				showCsinkManagerFilter={showCsinkManagerFilter}
			/>

			{/* <Stack className='tab-panel' value='my-network'> */}

			<Stack className='tab-panel'>
				<Stack className='filtersTab'>
					<Stack className='filter-Chips'>
						{subTabOptions.map(({ label, value, disabled }) => (
							<Chip
								key={value}
								disabled={disabled}
								onClick={() =>
									handleSubNetwork(value as NetworkEnumForHomePage)
								}
								color={paramsSubNetwork === value ? 'primary' : 'default'}
								label={label}
							/>
						))}
						<BiomassFilter isMultipleSelect />
					</Stack>

					<TimefilterHomeScreen />
				</Stack>

				<Stack className='stack-container'>
					{totalQuantities.map((data, index) => (
						<Box className='box-container' key={index}>
							<Typography variant='body2'>{data.label}</Typography>
							<Typography sx={{ marginLeft: theme.spacing(0.8) }}>
								{Number(data?.value) > 0 ? data.value : 0}
								{data?.unit ? data?.unit : ''}
							</Typography>
						</Box>
					))}
				</Stack>

				<Stack spacing={4}>
					<Stack className='chart-container'>
						{ChartLabelDataCollection?.length > 0 &&
							ChartLabelDataCollection.map((data) => (
								<NewChartCard
									key={data?.id}
									data={data}
									chartOptions={chartData(data?.id).options as ApexOptions}
									chartSeries={data?.allData?.map((item) => item.labelQuantity)}
									biocharProps={{
										biocharUnit,
										handleBiocharUnit,
										measuringUnitOptions,
									}}
								/>
							))}
					</Stack>
					<Stack className='chart-notification-container' direction='row'>
						<Stack direction='column' className='linar-chart-container'>
							<Stack
								direction='row'
								alignItems='center'
								justifyContent='space-between'>
								<Stack direction='row' alignItems='center' columnGap={2}>
									<Typography variant='h5'>All Sites</Typography>
									<Stack direction='row' columnGap={2}>
										{chipData.map((chip) => (
											<Chip
												key={chip.filterKey}
												label={chip.label}
												color={chip.param === 'true' ? 'primary' : 'default'}
												onClick={() => handleChipFilter(chip.filterKey)}
											/>
										))}
									</Stack>
								</Stack>
							</Stack>
							<Stack
								className='border-query linear-chart'
								sx={{
									width: { sm: '75%', md: '100%', lg: '100%' },
								}}>
								{currentLocationQuery?.isLoading ? (
									<Stack justifyContent='center' alignItems='center' py={10}>
										<CircularProgress size={35} />
									</Stack>
								) : (
									<GoogleMapWithMultipleMarker
										center={mapCenter ?? { lat: 0, lng: 0 }}
										markers={mapMarkers ?? []}
										mapContainerStyle={{
											width: '100%',
											height: 500,
											position: 'relative',
										}}
										showLabel={withLabelParams === 'true'}
										zoom={0}
										options={{
											minZoom: 2,
											maxZoom: 0,
											center: { lat: 0, lng: 0 },
										}}
									/>
								)}
							</Stack>
						</Stack>
						<Stack direction='column' className='notification-container'>
							<Stack className='notification-container-header' direction='row'>
								{/* <Typography variant='body2'>Notifications/Updates</Typography>
									<Button variant='text' className='clear-button'>
										Clear all
									</Button> */}
							</Stack>
							<Card elevation={0} className='border-query'>
								{/*<Stack className='card'>
										<Stack className='stack-container' sx={{ pl: 2 }}>
											{['Updates', 'Registry Updates', 'Pending Tasks'].map(
												(label, index) => (
													<Chip
														key={index}
														label={label}
														sx={{ height: '25px' }}
													/>
												)
											)}
										</Stack>
										{[1, 2, 3].map((notification) => (
											<CardActionArea
												className='notification-card-action-area'
												key={notification}>
												<Stack spacing={1} py={2}>
													<Typography variant='body1'>
														<EMAIL> sent a query.
													</Typography>
													<Typography
														variant='overline'
														sx={{
															color: theme.palette.neutral['300'],
															textTransform: 'none',
														}}>
														10 mins ago
													</Typography>
												</Stack>
											</CardActionArea>
										))}
										<Button
											className='view-all-button'
											sx={{ color: 'grey.600', justifyContent: 'end', mr: 2 }}>
											See All
										</Button>
									</Stack> */}
							</Card>
						</Stack>
					</Stack>
				</Stack>
			</Stack>
			{/* <TabPanel className='tab-panel' value='all-c-sink-manager'>
					Item Two
				</TabPanel> */}
			{/* </TabContext> */}
		</CustomeHome>
	)
}

const CustomeHome = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	paddingTop: theme.spacing(4),
	'.tabList': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 3),
	},
	'.filtersTab': {
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	'.tab-panel': {
		padding: theme.spacing(0, 3),
		'.stack-container': {
			flexDirection: 'row',
			gap: theme.spacing(2),
			margin: theme.spacing(1, 1, 3, 0),
			flexWrap: 'wrap',
			'.box-container': {
				display: 'flex',
				flexDirection: 'row',
				alignItems: 'center',
				borderRadius: theme.spacing(8),
				boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
					theme.palette.common.black,
					0.4
				)}`,
				padding: theme.spacing(1.25),
			},
		},

		'.filter-Chips': {
			flexDirection: 'row',
			gap: theme.spacing(2),
			padding: theme.spacing(2, 0, 3, 0),
		},
	},
	'.circle-icon': {
		height: 12,
		width: 12,
	},
	'.border-card': {
		borderRadius: theme.spacing(2),
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
	},
	'.border-query': {
		borderRadius: theme.spacing(2),
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
	},
	'.chart-container': {
		flexDirection: 'row',
		width: '93%',
		gap: theme.spacing(2),
		// flexWrap: 'wrap',
		'.card': {
			padding: theme.spacing(3),
			width: '40%',
			maxWidth: theme.spacing(60),
			height: theme.spacing(30),
			'.card-container': {
				height: '100%',
				alignItems: 'center',
				'.chart': {
					position: 'relative',
					width: '60%',
					height: '100%',
					justifyContent: 'center',
					marginLeft: theme.spacing(-3),
					'.chart-data': {
						position: 'absolute',
						height: 48,
						width: 48,
					},
				},
			},
		},
	},
	'.container': {
		gap: theme.spacing(1),
		'.full-width-stack': {
			// whiteSpace: 'nowrap',
		},
	},

	'.chart-notification-container': {
		justifyContent: 'space-between',
		alignItems: 'flex-start',
		flexWrap: 'wrap',
		height: '100%',
		width: '100%',
		rowGap: theme.spacing(8),
	},
	'.linar-chart-container': {
		minWidth: 600,
		width: '58%',
		gap: theme.spacing(2.5),
		'.linear-chart': {
			padding: theme.spacing(2),
		},
	},
	'.notification-container': {
		minWidth: 350,
		width: '40%',
		gap: theme.spacing(3),
		margin: theme.spacing(4.5, 0),
		'.notification-container-header': {
			justifyContent: 'space-between',
			alignItems: 'center',
			'.clear-button': {
				textTransform: 'none',
				padding: 0,
				color: theme.palette.primary.light,
				...theme.typography.caption,
			},
		},
		'.card': {
			padding: theme.spacing(2, 0),
			'.notification-card-action-area': {
				borderBottom: `${theme.spacing(0.25)} solid ${theme.palette.divider}`,
				padding: theme.spacing(0, 2),
			},
			'.view-all-button': {
				marginTop: theme.spacing(2),
				textTransform: 'none',
				...theme.typography.subtitle2,
			},
		},
	},
}))
