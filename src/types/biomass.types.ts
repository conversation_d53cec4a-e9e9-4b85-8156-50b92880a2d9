export interface ICrop {
	id: string
	name: string
	cropId?: string
	createdAt?: Date
	season: string
	seasonValue: string
	imageUrl: string | null
	type: string
	description: string
	csinkManagers: ICSinkManagers[]
	carbonSettingCount: number
	cropDensity: number | null
	imagePathUrl?: {
		id: string
		url: string
		path?: string
		createdTime?: string
	}
	biomassType: string
}
export interface ICSinkManagers {
	id: string
	name: string
	shortName: string
}
export enum BiomassTypeEnum {
	agriculturalResidue = 'agricultural_residue',
	invasiveSpecies = 'invasive_species',
	other = 'others',
}

export interface UpdateBiomassPayload {
	id: string
	biomassQuantity: number
}

export interface IAddBiomass {
	biomassType: string
	name?: string
	biomassImageId: string
	season?: string
	description?: string
	density?: number | null
}

export interface IBioCharContentDetail {
	id: string
	temperature: number
	density: number
	carbonPercentage: number
	technology: string
	hContent: number
	HByC: number
	ash: number
	nContent: number
	residenceTimeInMinutes: number
	HeatingTimeInMinutes: number
	labReportImages: LabReportInfo[]
	labReportDocs: LabReportInfo[]
	sContent: number
	ph: number
	bulkDensity: number
	organicCarbonPercentage: number
	inorganicCarbonPercentage: number
	waterHoldingCapacity: number
}

export interface LabReportInfo {
	id: string
	url: string
	uploadedBy: null
	path: string
}

export type BiomassOptionType = {
	label: string
	value: string
}

export enum fieldSizeEnum {
	Hectare = 'hectare',
	Acre = 'acre',
}
