import { IFileData } from '@/interfaces'

const { VITE_BASE_URL, VITE_IMAGE_PROXY_URL, VITE_PUBLIC_APP_ENV } = import.meta
	.env

export const BASE_URL = VITE_BASE_URL
export const initialToken = '...loading'
export const emptyToken = null

export const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
export const passwordRegex =
	/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/

export const defaultLimit = 25
export const defaultPage = 0
export const defaultIndex = 0
export const filterValue =
	'brightness(0) saturate(100%) invert(20%) sepia(87%) saturate(1481%) hue-rotate(343deg) brightness(96%) contrast(84%);' // alternative filter for color #9D392B
export const BASE_IMAGE_PROXY_URL = VITE_IMAGE_PROXY_URL
export const pageLimits = ['10', '25', '50', '100']

export const longWordMaxLimit = 70

const MANAGER = 'Manager'
const SUPERVISOR = 'Supervisor'
const COMPANY_ADMIN = 'Company Admin'

export const isDevOrQA = ['DEV', 'QA'].includes(VITE_PUBLIC_APP_ENV)

export enum userRoles {
	Admin = 'admin',
	BiomassAggregator = 'biomass_aggregator',
	CsinkManager = 'c_sink_manager',
	Manager = 'manager',
	Supervisor = 'supervisor',
	ArtisanPro = 'artisan_pro_admin',
	cSinkNetwork = 'network_admin',
	CirconomyEmployee = 'circonomy_employee',
	artisanProNetworkManager = 'artisan_pro_network_manager',
	kilnOperator = 'kiln_operator',
	artisanProOperator = 'artisan_pro_operator',
	csink_operator_farmer = 'csink_operator_farmer',
	csinkNetworkOperator = 'csink_network_operator',
	farmer = 'farmer',
	compliance_manager = 'compliance_manager',
	company_admin = 'company_admin',
	ceres_auditor = 'ceres_auditor',
	role_awaiting = 'role_awaiting',
}
export enum userRolesName {
	admin = 'Admin',
	biomass_aggregator = 'Biomass Aggregator',
	c_sink_manager = 'C sink Manager',
	manager = 'Manager',
	supervisor = 'Supervisor',
	artisan_pro_admin = 'Artisan Pro',
	network_admin = 'C Sink Network',
	circonomy_employee = 'Circonomy Employee',
	artisan_pro_network_manager = 'ArtisanPro Network Manager ',
	kiln_operator = 'Kiln Operator',
	artisan_pro_operator = 'ArtisanPro Operator',
	csink_operator_farmer = 'Csink Network Farmer',
	csink_network_operator = 'Csink Network Operator',
	farmer = 'farmer',
	compliance_manager = 'Compliance Manager',
	company_admin = 'Company Admin',
	ceres_auditor = 'Ceres Auditor',
	role_awaiting = 'role_awaiting',
}

export enum userRolesNameOrganizationSettings {
	admin = 'Admin',
	biomass_aggregator = 'Biomass Aggregator',
	c_sink_manager = COMPANY_ADMIN,
	manager = MANAGER,
	supervisor = SUPERVISOR,
	artisan_pro_admin = MANAGER,
	network_admin = MANAGER,
	circonomy_employee = 'Circonomy Employee',
	artisan_pro_network_manager = 'ArtisanPro Network Manager ',
	kiln_operator = 'Kiln Operator',
	artisan_pro_operator = SUPERVISOR,
	csink_operator_farmer = 'Csink Network Farmer',
	csink_network_operator = SUPERVISOR,
	farmer = 'farmer',
	compliance_manager = 'Compliance Manager',
	company_admin = COMPANY_ADMIN,
	ceres_auditor = 'Ceres Auditor',
	role_awaiting = 'Role Awaiting',
}

export enum userRolesNameUserManagement {
	admin = 'Admin',
	biomass_aggregator = 'Biomass Aggregator Admin',
	c_sink_manager = 'C sink Manager',
	manager = 'Manager',
	supervisor = 'Supervisor',
	artisan_pro_admin = 'Artisan Pro Admin',
	network_admin = 'C Sink Network Admin',
	circonomy_employee = 'Circonomy Employee',
	artisan_pro_network_manager = 'ArtisanPro Network Admin ',
	kiln_operator = 'Kiln Operator',
	artisan_pro_operator = 'ArtisanPro Operator',
	csink_operator_farmer = 'Csink Network Farmer',
	csink_network_operator = 'Csink Network Operator',
	farmer = 'Farmer',
	compliance_manager = 'Compliance Manager',
	company_admin = 'Company Admin',
	ceres_auditor = 'Ceres Auditor',
	role_awaiting = 'Role Awaiting',
}

export enum dateFormats {
	dd_MM_yyyy_with_time_in_bracket = 'dd/MM/yyyy (hh:mm)',
	dd_MM_yyyy_with_time_in_bracket_AM_PM = 'dd/MM/yyyy (hh:mm a)',
	dd_MM_yyyy = `dd/MM/yyyy`, // 14/09/2023
	dd_MMM_yyyy = 'dd MMM yyyy', // 14 Sep 2023
	dd_MM_yyyy_with_Hyphen = 'dd-MM-yyyy',
	MM_yyyy_with_dot = 'MM.yyyy',
	yyyy_MM_dd = 'yyyy/MM/dd',
	onlyTime = 'p', // 11:04 AM
	time24HrFormat = 'HH:mm',
	MMM_dd_yyyy = 'MMM dd, yyyy',
	MMMM_dd_yyyy = 'MMMM dd, yyyy',
}

export enum containerShapesEnum {
	cuboid = 'cuboid',
	cylinder = 'cylinder',
	rectangular = 'rectangular_frustum',
	conical = 'conical',
	other = 'other',
}

export const containerShapeLabel = {
	cuboid: 'Cuboidal',
	cylinder: 'Cylinder',
	conical: 'Conical',
	rectangular_frustum: 'Rectangular',
	other: 'Other',
}

export const containerShapeLabelValue = [
	{ label: 'Cuboidal', value: containerShapesEnum.cuboid },
	{ label: 'Cylinder', value: containerShapesEnum.cylinder },
	{ label: 'Conical', value: containerShapesEnum.conical },
	{ label: 'Rectangular', value: containerShapesEnum.rectangular },
	{ label: 'Other', value: containerShapesEnum.other },
]

export enum kilnShapeEnum {
	Rectangular = 'rectangular_frustum',
	Conical = 'conical',
	Cylindrical = 'cylindrical',
	Pyramidal = 'rectangular', // Pyramidal = rectangular because api payload is set as rectangular.
}
export enum showKlinShapeLabelEnum {
	rectangular_frustum = 'rectangular_frustum',
	conical = 'conical',
	cylindrical = 'cylindrical',
	pyramidal = 'pyramidal',
	rectangular = 'rectangular',
}
export enum kilnTypeEnum {
	KonTiki = 'kontiki',
	Pit = 'pit',
	RoCC = 'RoCC',
}

export const KilnTypeLabelValue = [
	{ label: 'Kon Tiki', value: kilnTypeEnum.KonTiki, temperature: 650 },
	{ label: 'Pit', value: kilnTypeEnum.Pit, temperature: 600 },
	{ label: 'RoCC', value: kilnTypeEnum.RoCC, temperature: 0 },
]

export const KilnShapeLabelValue = [
	{ label: 'Conical', value: kilnShapeEnum.Conical },
	{ label: 'Pyramidal', value: kilnShapeEnum.Pyramidal },
	{ label: 'Rectangular', value: kilnShapeEnum.Rectangular },
	{ label: 'Cylindrical', value: kilnShapeEnum.Cylindrical },
]

export enum measuringUnitEnum {
	kg = 'kg',
	ltr = 'ltr',
	m3 = 'm3',
	mm = 'mm',
	cm = 'cm',
	m = 'm',
	cm3 = 'cm3',
	mm3 = 'mm3',
}
export const measuringUnitsValue = [
	{ label: 'm', value: measuringUnitEnum.m },
	{ label: 'cm', value: measuringUnitEnum.cm },
]
export enum EntityTabEnum {
	bags = 'bags',
	containers = 'containers',
	vehicles = 'vehicles',
	preferredBiomass = 'preferredBiomass',
	admins = 'admins',
	buyers = 'buyers',
	kilns = 'kilns',
	samplingContainer = 'samplingContainer',
	biomassSource = 'biomassSource',
	methaneCompensationStrategy = 'methaneCompensationStrategy',
	biomassProcessingStrategy = 'biomassProcessingStrategy',
	mixingTypes = 'mixingTypes',
	applicationTypes = 'applicationTypes',
	assignkiln = 'assignKiln',
	operator = 'operator',
	assignMeasuringContainer = 'assignMeasuringContainer',
}

export enum farmTabTypes {
	all = 'all',
	kml = 'kml',
}
export enum settingsTab {
	kiln = 'kiln',
	measuringContainer = 'MeasuringContainer',
	samplingContainer = 'SamplingContainer',
	generateCertificate = 'GenerateCertificate',
	addConstants = 'AddConstants',
}

export enum BatchStatusTypeEnum {
	Approved = 'approved',
	Rejected = 'rejected',
	NotAssessed = 'not_assessed',
	Started = 'started',
	PartiallyCompleted = 'partially-completed',
	AdminRejected = 'admin-rejected',
	AdminApproved = 'admin-approved',
	AdminReasses = 'admin_reassess',
	Compensated = 'compensate',
}
export enum MediaActionStatus {
	rejected = 'rejected',
	accepted = 'accepted',
	deleted = 'deleted',
}
export enum ActionTypes {
	ADD_VEHICLE = 'ADD_VEHICLE',
	ADD_MIXING_ARTISAN = 'ADD_MIXING_ARTISAN',
	ADD_MIXING_CSINK = 'ADD_MIXING_CSINK',
	ADD_PACKAGING_ARTISAN = 'ADD_PACKAGING_ARTISAN',
	ADD_PACKAGING_CSINK = 'ADD_PACKAGING_CSINK',
	ADD_DISTRIBUTION_FARMER_ARTISAN = 'ADD_DISTRIBUTION_FARMER_ARTISAN',
	ADD_DISTRIBUTION_FARMER_CSINK = 'ADD_DISTRIBUTION_FARMER_CSINK',
	ADD_DISTRIBUTION_OTHER_ARTISAN = 'ADD_DISTRIBUTION_OTHER_ARTISAN',
	ADD_DISTRIBUTION_OTHER_CSINK = 'ADD_DISTRIBUTION_OTHER_CSINK',
	BIOMASS_COLLECTION = 'BIOMASS_COLLECTION',
	BATCH_CREATION = 'BATCH_CREATION',
	ADD_TEMPERATURE = 'ADD_TEMPERATURE',
	END_PROCESS = 'END_PROCESS',
	ADD_PROCESS_DETAILS = 'ADD_PROCESS_DETAILS',
	UPDATE_PROCESS_BIOMASS_QUANTITY = 'UPDATE_PROCESS_BIOMASS_QUANTITY',
	ADD_BIOCHAR = 'ADD_BIOCHAR',
	ADD_BIOCHAR_CSINK = 'ADD_BIOCHAR_CSINK',
	BATCH_CREATION_CSINK = 'BATCH_CREATION_CSINK',
	BIOMASS_COLLECTION_CSINK = 'BIOMASS_COLLECTION_CSINK',
	ADD_FARMER_FARM_CROP_CSINK = 'ADD_FARMER_FARM_CROP_CSINK',
	ADD_FARMER_FARM_CROP_ARTISAN = 'ADD_FARMER_FARM_CROP_ARTISAN',
	ADD_FPU = 'ADD_FPU',
	REPLENISH_FARMER_DISTRIBUTION = 'REPLENISH_FARMER_DISTRIBUTION',
	REPLENISH_OTHER_DISTRIBUTION = 'REPLENISH_OTHER_DISTRIBUTION',
	ADD_SITE_APPLICATION_ARTISAN = 'ADD_SITE_APPLICATION_ARTISAN',
}

export enum AppType {
	CSinkNetworkApp = 'CSinkNetworkApp',
	ArtisanProApp = 'ArtisanProApp',
}
export enum addConstantTabs {
	productionConstants = 'productionConstants',
	carbonCreditConstants = 'carbonCreditConstants',
}
export enum shreddingTypeEnum {
	Manual = 'manual',
	Machine = 'machine',
}

export enum dryingTypeEnum {
	SunDrying = 'sundrying',
	MachineDrying = 'machine_drying',
}
export enum MethaneType {
	Avoidance = 'avoidance',
	Compensation = 'compensation',
}

export enum CompensationType {
	SPC = 'spc',
	TreePlanting = 'tree_planting',
	Others = 'others',
}

export enum CertificateTypeEnum {
	CSI_ENDORSEMENT = 'csi_endorsement',
	CERES = 'ceres',
	CERES_AUDIT = 'ceres_audit_finding_report',
	PDD = 'pdd',
	VALIDATION_STATEMENT = 'validation_statement',
	VALIDATION_REPORT = 'validation_report',
	PDD_FINDING = 'pdd_finding_report',
}

export const certificateTypeList = [
	{
		label: 'CSI Endorsement Certificate',
		value: CertificateTypeEnum.CSI_ENDORSEMENT,
	},
	{
		label: 'CERES Certificate',
		value: CertificateTypeEnum.CERES,
	},
	{
		label: 'CERES Audit Finding Report',
		value: CertificateTypeEnum.CERES_AUDIT,
	},
	{
		label: 'PDD',
		value: CertificateTypeEnum.PDD,
	},
	{
		label: 'Validation Statement',
		value: CertificateTypeEnum.VALIDATION_STATEMENT,
	},
	{
		label: 'Validation Report',
		value: CertificateTypeEnum.VALIDATION_REPORT,
	},
	{
		label: 'PDD Finding Report',
		value: CertificateTypeEnum.PDD_FINDING,
	},
]

export const APP_DEV_URL = `https://circonomy-admin.web.app/`
export const APP_STAGE_URL = `https://stage-admin-circonomy.web.app/`
export const APP_PROD_URL = `https://prod-admin-circonomy.web.app/`

export enum fuelTypes {
	DIESEL = 'diesel',
	PETROL = 'petrol',
	NON_MOTORISED = 'non_motorised',
	CNG = 'cng',
	ELETRIC = 'electric',
	NONE = 'none',
	BULLOCK_CART = 'bullock_cart',
	OTHER = 'other',
}
export enum fuelTypes {
	non_motorised = 'Non Motorised',
	other = 'Other',
	manual = 'Manual',
	bullock_cart = 'Bullock Cart',
}

export const AllFileTypes = [
	'png',
	'jpg',
	'jpeg',
	'webp',
	'svg',
	'pdf',
	'doc',
	'docx',
	'txt',
	'ppt',
	'pptx',
	'xls',
	'xlsx',
	'csv',
]

export enum StageStatus {
	rejected = 'rejected',
	approved = 'approved',
	pending = 'pending',
	notStarted = 'not_started',
}

export enum StageStatusName {
	rejected = 'Rejected',
	approved = 'Approved',
	pending = 'Pending',
	not_started = 'Not Started',
}

export enum CompanyStages {
	registration = 'registration',
	contract = 'contract',
	dmrvCustomization = 'dmrv_customization',
}
export interface ICompanyDetails {
	id?: string
	name?: string
	countryCode?: string
	phoneNumber?: string
	email?: string
	state?: string
	pinCode?: string
	country?: string
	companyLogo?: IFileData | null
	coiDocument?: IFileData | null
	ndaDocument?: IFileData | null
	commercialDocument?: IFileData | null
	serviceType?: string
	registry?: string
	isTEAEnabled?: boolean | null
	isLCAEnabled?: boolean | null
	isFinanceToolEnabled?: boolean | null
	admins?: IAdmin[]
	createdAt?: string
	stages: ICompanyStage[]
	status?: string
	stage?: string
}

export interface IAdmin {
	id?: string
	name?: string
	countryCode?: string
	phoneNumber?: string
	email?: string
	profileImageId?: string
	profileImage?: IFileData | null
}

export interface ICompanyStage {
	stage?: CompanyStages
	createdAt?: string | null
	status?: StageStatus
	isCompleted?: boolean
	assessedAt?: string | null
	assessedBy?: {
		id?: string | null
		name?: string | null
	}
}

export enum ServiceTypeEnum {
	industrial = 'industrial',
	artisanal = 'artisanal',
	iot_artisanal = 'iot_artisanal',
}

export enum ServiceTypeNames {
	industrial = 'Industrial',
	artisanal = 'Artisanal',
	iot_artisanal = 'IoT Artisanal',
}

export enum RegistryTypeEnum {
	CSI = 'csi',
	Puro = 'puro',
	Isometric = 'isometric',
}

export enum RegistryTypeNames {
	csi = 'CSI',
	puro = 'Puro',
	isometric = 'Isometric',
}

export enum entitiesRoles {
	CsinkManager = 'csink_manager',
	ArtisanPro = 'artisan_pro',
	cSinkNetwork = 'csink_network',
}

export enum entitiesRolesNames {
	csink_manager = 'Company',
	artisan_pro = 'Artisan Pro',
	csink_network = 'CSink Network',
}
export enum UploadType {
	mixing_packaging = 'mixing_packaging',
	misc = 'misc',
}

export enum StatusFilterOptions {
	all = 'all',
	Active = 'active',
	Suspended = 'suspended',
}

export enum EntityEnum {
	company = 'company',
	artisanPro = 'artisanPro',
	cSinkNetwork = 'cSinkNetwork',
}

export enum OrganizationUser {
	admin = 'admin',
	circonomy_employee = 'circonomy_employee',
	compliance_manager = 'compliance_manager',
	ceres_auditor = 'ceres_auditor',
	other = 'other',
	supervisor = 'supervisor',
	manager = 'manager',
}

export enum FieldTypeEnum {
	assignedBiomass = 'assignedBiomass',
	applicationTypes = 'applicationTypes',
	mixingTypes = 'mixingTypes',
}
export enum NetworkType {
	artisanPro = 'artisan-pro',
	cSinkNetwork = 'c-sink-network'
}
export type AssignNetworkDetails = {
	role: string;
	networks: string[];
	userType: string;
	networkName?: string;
	networkType?: string;
  };