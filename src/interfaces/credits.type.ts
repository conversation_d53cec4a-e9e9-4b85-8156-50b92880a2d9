export interface ICarbonCredits {
	count: number
	stocks: Stock[]
}

export interface Sink {
	id: string
	status: string
}

export interface Stock {
	stockId: string
	projectId: string
	biocharQuantity: number
	carbonCredits: number
	cropId: string
	cropName: string
	sinkId: null | string
	blendingMatrixId: null | string
	status: string
	internalProjectName: string
	internalProjectId: string
	registryProjectName?: string
	sinks: Sink[]
}
