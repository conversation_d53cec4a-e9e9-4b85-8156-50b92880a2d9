import { Nullable } from '@/types'
import { ManagerDetail } from './artisanProNetworkDetails.type'
import { IImage } from './image.type'
import { entitiesRoles } from '@/utils/constant'
import { IFileData } from './mixing.type'

export interface IBiomassAggregatorResponse {
	count: number
	allBACount: number
	suspendedBACount: number
	unSuspendedBACount: number
	limit: number
	page: number
	biomassAggregators: BiomassAggregator[]
}

export interface BiomassReference {
	id: string
	biomassTypeId: string
	biomassName: string
	fieldSize: number
	fieldSizeUnit: string
	biomassQuantity: number
	createdById: string
	createdByName: string
	documents?: IFileData[]
}
export interface BiomassAggregator {
	id: string
	name: string
	biomassAggregatorManagerId: null
	email: null
	certificateUrl: null
	certificateStatus: null
	CSinkCount: number
	farmersCount: number
	trained: boolean
	trainingImages: null
	locationName: string
	shortName: string
	suspended: boolean
	managerDetails: ManagerDetails[]
	biomassReference?: BiomassReference[]
}

export interface IBiomassAggregatorDetailResponse {
	id: string
	name: string
	email: string
	trained: boolean
	trainingImages: null
	locationName: string
	location: string
	phoneNo: null
	countryCode: null
	shortName: string
	pendingFarmerCount: number
	managerDetails: ManagerDetail[]
	suspended: boolean
	csnCount: number
	farmerCount: number
	apnCount: number
	certificateUrl: null
}

export interface ICSinkManagerResponse {
	count: number
	csinkManagers: CsinkManager[]
}
export interface IAPNResponse {
	count: number
	artisanProNetworks: artisanProNetworks[]
}
export interface artisanProNetworks {
	id: string
	name: string
	address: string
	biomassAggregatorName: string
	artisanProCount: number
	totalBiocharProduced: number
	totalBiomassDropped: number
	siteCount: number
}
export interface ICSinkNetworkResponse {
	count: number
	network: networks[]
}

export interface IEntitytabsCount {
	csinkManagerCount?: number
	baCount?: number
	csinkNetworkCount?: number
	artisanProNetworkCount?: number
	artisanProCount?: number
}
export interface IUserTabsCount {
	csinkManagers: number
	biomassAggregators: number
	networkAdmins: number
	operators: number
	farmers: number
}
export interface networks {
	id: string
	name: string
	methaneCompensationStrategy: string
	bighaInHectare: number
	locationName: string
	biomassAggregatorName: string
}

export interface IAPsResponse {
	count: number
	artisanPros: artisanPros[]
}

export interface artisanPros {
	id: string
	name: string
	artisianProNetworkName: string
	methaneCompensationStrategy: string
	bighaInHectare: number
	artisianProNetworkID: string
	locationName: string
}
export interface artisanProNetworks {
	id: string
	name: string
	address: string
	biomassAggregatorName: string
	artisanProCount: number
	totalBiocharProduced: number
	totalBiomassDropped: number
	siteCount: number
}

export interface CropforCsinkManager {
	cropId?: string
	cropName?: string
}
export interface CsinkManager {
	id: string
	name: string
	locationName: string
	location: string
	crops?: CropforCsinkManager[]
	shortName: string
	managerDetails: ManagerDetails[]
	suspended: boolean
	mixingTypes: IManagerMixingType[]
	applicationTypes: IManagerApplicationType[]
	biomassReference?: BiomassReference[]
}

// common
export interface ManagerDetails {
	managerId: string
	managerName: string
	managerPhone: null | string
	managerEmail: string
	trained: boolean
	profileImageUrl?: IImage
	trainingImages: IImage[]
	countryCode: null | string
	certificateImage: IImage
	certificateStatus: string
	accountType: string
}

export enum EntityEnum {
	cSinkManager = 'cSinkManager',
	ba = 'ba',
	apn = 'apn',
	aps = 'aps',
	cSinkNetwork = 'cSinkNetwork',
	mixingType = 'mixingType',
	applicationsType = 'applicationsType',
}

export type IManagerMixingType = {
	id: string
	name: string
	biocharOnly: boolean
	type: string
	otherMixName: Nullable<string>
	fixedRatio: boolean
	density: Nullable<number>
	category: string
}
export interface IBiomassReference {
	id: string
	biomassTypeId: string
	biomassName: string
	fieldSize: number
	fieldSizeUnit: string
	biomassQuantity: number
	createdById: string
	createdByName: string
}
export type IManagerApplicationType = {
	id: string
	category: string
	type: string
	csinkId: string
	matrixId: string
}

export type EntitiesResponse = {
	count: number
	entities: Entity[]
}

export type Entity = {
	id: string
	name: string
	entityType: entitiesRoles
	location: string
	createdAt: string
	biomassAggregatorId: string
	csinkManagerId: string
	artisanProNetworkId: string
	managers: Manager[]
	suspended: boolean
	coordinate: {
		x: number
		y: number
	}
}

type Manager = {
	id: string
	name: string
	upload_id: string
	profileImage: ImageUrl
}

type ImageUrl = {
	id: string
	url: string
	path: string
	deletionStatus: string | null
	deletionReason: string | null
	fileType: string
	createdAt: string | null
}
